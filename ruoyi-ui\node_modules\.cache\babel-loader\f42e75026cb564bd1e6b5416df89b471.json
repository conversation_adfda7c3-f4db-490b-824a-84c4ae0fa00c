{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue", "mtime": 1755499098453}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_task", "require", "_plan", "_xctgDriverCar", "_elementUi", "_qrcodejs", "_interopRequireDefault", "name", "data", "factoryConfirmDialogVisible", "factoryConfirmForm", "companyName", "taskNo", "applyNo", "planNo", "taskType", "unloadingWorkNo", "unloadingTime", "spec1Length", "spec2Width", "totals", "total", "totalUnit", "processType", "heatNo", "steelGrade", "axles", "remark", "taskStatus", "carNum", "stockOutSpec1Length", "stockOutSpec2Width", "stockOutTotals", "stockOutTotalUnit", "stockOutTotal", "stockOutProcessType", "stockOutHeatNo", "stockOutSteelGrade", "stockOutAxles", "stockOutRemark", "handledMaterialName", "sourceCompany", "receiveCompany", "showDropdown", "extraOption", "deductWeight", "optionDialogVisible", "searchForm", "optionList", "editDoorManStatus", "editFactoryStatus", "driverInfo", "id", "idCard", "phone", "gender", "company", "photo", "driverLicenseImgs", "vehicleLicenseImgs", "carInfo", "taskMaterials", "taskLogs", "isdoorMan", "dispatchId", "taskInfoForm", "measureFlag", "backupTaskMaterials", "selectedOption", "planForm", "processTypeOptions", "filteredProcessTypeOptions", "searchProcessTypeQuery", "directSupplyPlanList", "editingRow", "selectedRows", "directSupplyParams", "computed", "displayProcessTypeOptions", "hasSelectedItems", "length", "materialNames", "map", "item", "materialName", "join", "materialSpecs", "materialSpec", "activated", "console", "log", "resetTaskInfoForm", "_this$$route$query", "$route", "query", "planType", "validDoorMan", "initializeData", "methods", "getDirectSupplyPlanAndTask", "_this", "leaveTask0", "directSupplyTaskNo", "getDirectSupplyPlanAndTaskDetail", "then", "res", "code", "rows", "$message", "error", "message", "catch", "err", "_this2", "$store", "getters", "roles", "for<PERSON>ach", "_this3", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "_t", "w", "_context", "n", "p", "getTaskInfo", "getTaskmaterialList", "getPlanInfo", "uploadFactoryConfirmForm", "getTaskLogList", "getProcessType", "v", "a", "_this4", "doorman<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planNum", "doormanReceiveNumIn", "gross", "secGross", "<PERSON><PERSON><PERSON>", "tare", "Date", "openNewWindow", "newWindowUrl", "window", "open", "getDirectSupplyList", "_this5", "_callee2", "leavePlan", "_iterator", "_step", "leavePlanMaterial", "response", "_t2", "_t3", "_context2", "getDirectSupplyPlans", "_createForOfIteratorHelper2", "s", "done", "value", "getPlanMaterials", "e", "f", "filterProcessType", "filter", "includes", "_this6", "getProcessList", "processname", "label", "_this7", "_callee3", "_t4", "_context3", "detailPlan", "openFactoryConfirmDialog", "submitFactoryConfirm", "_this8", "submitData", "isDirectSupply", "leaveTask", "leaveTaskMaterial", "directSupplyTask", "secGrossTime", "sex", "mobilePhone", "idCardNo", "vehicleEmissionStandards", "faceImg", "drivingLicenseImg", "driverLicenseImg", "directSupplyTaskMaterialList", "handleUnload", "success", "submitStockOutConfirm", "_this9", "handleStockOut", "handleFactoryConfirm", "_this0", "warning", "leaveTaskLog", "logType", "info", "factoryTaskInfo", "param", "taskMaterialList", "leaveLog", "addLeaveLogAndEditTaskMaterialsAndUpdateTask", "handleDoorManConfirm", "_this1", "doorManTaskInfo", "leaveTime", "toISOString", "slice", "replace", "enterTime", "handleDoorManMeasureConfirm", "_this10", "creatQrCode", "qrCode<PERSON>ontent", "$refs", "qrCode", "innerHTML", "YSqrCode", "QRCode", "text", "width", "height", "colorDark", "colorLight", "correctLevel", "CorrectLevel", "H", "_this11", "taskLog", "getTaskLogs", "logs", "finishedLogs", "otherLogs", "concat", "_toConsumableArray2", "_this12", "_callee4", "leaveMaterial", "_t5", "_context4", "getTaskmaterials", "editDoorManRow", "row", "_backup", "JSON", "parse", "stringify", "editFactoryRow", "backupMaterials", "cancelDoorManEdit", "Object", "assign", "cancelFactoryEdit", "saveDoorManRowIn", "_this13", "_iterator2", "_step2", "saveDoorManRow", "_this14", "_iterator3", "_step3", "saveFactoryRow", "_this15", "_callee5", "_t6", "_context5", "getTask", "licensePlateColor", "$nextTick", "getStatusText", "standard", "standardMap", "getPlanStatusText", "getEmissionStandardsText", "getEmissionStandardsTagType", "typeMap", "getMaterialStatusText", "status", "statusMap", "getMaterialStatusType", "getLogColor", "logTypeColorMap", "type", "cancel", "$router", "go", "getTaskDetail", "handleShowDropdownChange", "val", "openOptionDialog", "_this16", "loadOptions", "optionTable", "clearSelection", "handleOptionSelection", "selection", "lastSelected", "toggleRowSelection", "confirmOptionSelection", "_this17", "planStatus", "getBusinessCategoryText", "category", "categoryMap", "searchOptions", "_this18", "searchPlanNo", "toLowerCase", "searchApplyNo", "searchReceiveCompany", "toString", "matchPlanNo", "matchApplyNo", "matchReceiveCompany", "resetSearch", "getTaskTypeText", "handleSelectionChange", "handleNonMeasureFactoryConfirm", "_this19", "isHandled", "factoryReceiveNum", "openNewTaskWindow", "BigInt", "url"], "sources": ["src/views/leave/plan/task.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\" style=\"display: flex; align-items: center; justify-content: flex-start;\">\r\n        <h2>派车任务详情</h2>\r\n        <el-tag size=\"medium\" style=\"margin-left: 20px; margin-top: 10px;\">\r\n          任务状态： {{ getStatusText(taskInfoForm.taskStatus) }}\r\n        </el-tag>\r\n      </div>\r\n\r\n      <!-- 任务流程图部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">任务流程</div>\r\n        <div class=\"process-flow-container\">\r\n          <!-- <img style=\"width: 100%; max-height: 400px; object-fit: contain;\" :src=\"require('@/assets/images/task-flow-chart.png')\" /> -->\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 通行证二维码部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">通行证二维码</div>\r\n        <div class=\"qrcode-container\">\r\n          <div ref=\"qrCode\" class=\"qrcode\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 司机信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">司机信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"姓名\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 姓名</template>\r\n            {{ taskInfoForm.driverName }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"手机号\">\r\n            <template slot=\"label\"><i class=\"el-icon-mobile-phone\"></i> 手机号</template>\r\n            {{ taskInfoForm.mobilePhone }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"身份证号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 身份证号</template>\r\n            {{ taskInfoForm.idCardNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"性别\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 性别</template>\r\n            {{ taskInfoForm.sex === 1 ? '男' : '女' }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"所属单位\">\r\n            <template slot=\"label\"><i class=\"el-icon-office-building\"></i> 所属单位</template>\r\n            {{ taskInfoForm.companyName }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <!-- 司机照片和证件照片 -->\r\n        <div class=\"driver-photos\"\r\n          v-if=\"driverInfo.photo || driverInfo.driverLicenseImgs || driverInfo.vehicleLicenseImgs\">\r\n          <div class=\"photo-item\" v-if=\"driverInfo.photo\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 司机照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.faceImg\" alt=\"司机照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.faceImg\" fit=\"contain\" fallback=\"\"\r\n                :preview-src-list=\"[taskInfoForm.faceImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n          <div class=\"photo-item\" v-if=\"driverInfo.driverLicenseImgs\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 驾驶证照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.driverLicenseImg\" alt=\"驾驶证照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.driverLicenseImg\" fit=\"contain\"\r\n                fallback=\"\" :preview-src-list=\"[taskInfoForm.driverLicenseImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n          <div class=\"photo-item\" v-if=\"driverInfo.vehicleLicenseImgs\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 行驶证照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.drivingLicenseImg\" alt=\"行驶证照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.drivingLicenseImg\" fit=\"contain\"\r\n                fallback=\"\" :preview-src-list=\"[taskInfoForm.drivingLicenseImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 车辆信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">车辆信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"车牌号\" v-if=\"taskInfoForm.carNum != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-truck\"></i> 车牌号</template>\r\n            <el-tag type=\"primary\">{{ taskInfoForm.carNum }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"车牌颜色\" v-if=\"taskInfoForm.licensePlateColor != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-takeaway-box\"></i> 车牌颜色</template>\r\n            {{ taskInfoForm.licensePlateColor }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆道路运输证号\" v-if=\"taskInfoForm.trailerId != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 运输证号</template>\r\n            {{ taskInfoForm.trailerId }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"挂车号牌\" v-if=\"taskInfoForm.trailerNumber\">\r\n            <template slot=\"label\"><i class=\"el-icon-truck\"></i> 挂车号牌</template>\r\n            <el-tag type=\"info\">{{ taskInfoForm.trailerNumber }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"挂车道路运输证号\" v-if=\"taskInfoForm.trailerId\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 挂车运输证号</template>\r\n            {{ taskInfoForm.trailerId }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"轴型\" v-if=\"taskInfoForm.axisType != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-data-line\"></i> 轴型</template>\r\n            {{ taskInfoForm.axisType }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"货车自重\" v-if=\"taskInfoForm.driverWeight != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-heavy-rain\"></i> 货车自重</template>\r\n            {{ taskInfoForm.driverWeight }} kg\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"车货总质量限值\" v-if=\"taskInfoForm.maxWeight != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-opportunity\"></i> 总质量限值</template>\r\n            {{ taskInfoForm.maxWeight }} kg\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆排放标准\" v-if=\"taskInfoForm.vehicleEmissionStandards != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-magic-stick\"></i> 排放标准</template>\r\n            <el-tag :type=\"getEmissionStandardsTagType(taskInfoForm.vehicleEmissionStandards)\">\r\n              {{ getEmissionStandardsText(taskInfoForm.vehicleEmissionStandards) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"发动机号\" v-if=\"taskInfoForm.engineNumber != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-set-up\"></i> 发动机号</template>\r\n            {{ taskInfoForm.engineNumber }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆识别代码\" v-if=\"taskInfoForm.vinNumber != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-document-checked\"></i> 车辆识别代码</template>\r\n            {{ taskInfoForm.vinNumber }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 任务物资列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">物资列表</div>\r\n        <el-table :data=\"taskMaterials\" style=\"width: 100%\" border @selection-change=\"handleSelectionChange\">\r\n          <!-- <el-table-column type=\"selection\" width=\"55\" v-if=\"measureFlag == 0\">\r\n          </el-table-column> -->\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"150\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialSpec\" label=\"物资型号规格\" width=\"150\">\r\n          </el-table-column>\r\n          <!-- v-if=\"measureFlag == 0\" -->\r\n          <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"单位\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"门卫出厂确认数量\" width=\"230\"\r\n            v-if=\"taskInfoForm.taskStatus >= 4 && measureFlag == 0 && taskInfoForm.taskType != 2\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.doormanReceiveNum\" :min=\"0\" controls-position=\"right\"\r\n                :disabled=\"!isdoorMan && taskInfoForm.taskStatus !== 4\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNumIn\" label=\"门卫入厂确认数量\" width=\"230\"\r\n            v-if=\"measureFlag == 0 && taskInfoForm.taskType !== 1 && taskInfoForm.taskStatus >= 5\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.doormanReceiveNumIn\" :min=\"0\" controls-position=\"right\"\r\n                :disabled=\"!isdoorMan && taskInfoForm.taskStatus !== 5\" />\r\n            </template>\r\n          </el-table-column>\r\n          <!-- v-if=\"measureFlag == 0 && taskInfoForm.taskType == 2\" -->\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"分厂确认数量\" width=\"230\"\r\n            v-if=\"measureFlag == 0 && taskInfoForm.taskStatus > 7 && (taskInfoForm.taskType == 3 || taskInfoForm.taskType == 2)\">\r\n            <!-- <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.doormanReceiveNum\" :min=\"0\" controls-position=\"right\" disabled />\r\n            </template> -->\r\n          </el-table-column>\r\n          <el-table-column prop=\"remark\" label=\"备注\">\r\n          </el-table-column>\r\n\r\n          <!-- v-if=\"taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5 && (measureFlag == 0 && taskInfoForm.taskType == 2 && taskInfoForm.taskStatus == 7)\" -->\r\n          <!-- <el-table-column v-if=\"measureFlag == 0 && (taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5)\"\r\n            label=\"操作\" width=\"200\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex; flex-wrap: wrap; gap: 4px;\">\r\n\r\n                <div v-if=\"editingRow === scope.row\">\r\n                  <el-button size=\"mini\" type=\"success\" @click=\"saveDoorManRow(scope.row)\">保存</el-button>\r\n                  <el-button size=\"mini\" @click=\"cancelDoorManEdit(scope.row)\">取消</el-button>\r\n                </div>\r\n\r\n                <div v-else>\r\n                  <el-button v-hasPermi=\"['leave:task:doorManConfirm']\" size=\"mini\" type=\"primary\"\r\n                    @click=\"editDoorManRow(scope.row)\">门卫编辑</el-button>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column> -->\r\n        </el-table>\r\n\r\n        <div class=\"btn-wrapper\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 4\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"saveDoorManRow\" class=\"dispatch-btn\">\r\n            <!-- :disabled=\"!hasSelectedItems\" -->\r\n            门卫出厂确认\r\n          </el-button>\r\n        </div>\r\n        <div class=\"btn-wrapper\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 5\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"saveDoorManRowIn\" class=\"dispatch-btn\">\r\n            <!-- :disabled=\"!hasSelectedItems\" -->\r\n            门卫入厂确认\r\n          </el-button>\r\n        </div>\r\n        <div class=\"button-container\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 7\">\r\n          <el-button type=\"primary\" @click=\"handleNonMeasureFactoryConfirm\">\r\n            分厂确认\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1\">\r\n        <div class=\"section-title\">计量信息</div>\r\n        <div class=\"info-footer\" style=\"margin-top: 20px;\" v-if=\"measureFlag == 1\">\r\n          <el-descriptions :column=\"3\" border>\r\n            <el-descriptions-item label=\"皮重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.tare != null\">\r\n              {{ taskInfoForm.tare + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.gross != null\">\r\n              {{ taskInfoForm.gross + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.netWeight != null\">\r\n              {{ taskInfoForm.netWeight + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.tareTime != null\">\r\n              {{ taskInfoForm.tareTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.grossTime != null\">\r\n              {{ taskInfoForm.grossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.netWeight != null\">\r\n              {{ taskInfoForm.grossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重（复磅）\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.secTare != null\">\r\n              {{ taskInfoForm.secTare + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重（复磅）\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.secGross != null\">\r\n              {{ taskInfoForm.secGross + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secNetWeight != null\">\r\n              {{ taskInfoForm.secNetWeight + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secTareTime != null\">\r\n              {{ taskInfoForm.secTareTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secGrossTime != null\">\r\n              {{ taskInfoForm.secGrossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secNetWeightTime != null\">\r\n              {{ taskInfoForm.secNetWeightTime }}\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n          <!-- v-if=\"taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5\" -->\r\n          <div class=\"btn-wrapper\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 4\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleDoorManMeasureConfirm\" class=\"dispatch-btn\">\r\n              门卫出厂确认\r\n            </el-button>\r\n          </div>\r\n          <div class=\"btn-wrapper\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 5\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleDoorManMeasureConfirm\" class=\"dispatch-btn\">\r\n              门卫入厂确认\r\n            </el-button>\r\n          </div>\r\n          <!-- 新增分厂确认按钮 -->\r\n          <!-- <div class=\"btn-wrapper\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"openFactoryConfirmDialog\" class=\"dispatch-btn\">\r\n              分厂确认\r\n            </el-button>\r\n          </div> -->\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 可编辑的出库信息表单 -->\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 2\">\r\n        <div class=\"section-title\">出库信息</div>\r\n\r\n        <el-form :model=\"factoryConfirmForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input v-model=\"factoryConfirmForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input v-model=\"factoryConfirmForm.carNum\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"taskMaterials.map(item => item.materialName).join(' ')\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"皮重(t)\">\r\n                <el-input :value=\"taskInfoForm.tare\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"planForm.planType == 3\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物料规格\">\r\n                <el-input :value=\"taskMaterials.map(item => item.materialSpec).join(' ')\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.sourceCompany\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\" v-if=\"taskInfoForm.taskType\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutSpec1Length\" placeholder=\"请输入规格\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutTotal\" placeholder=\"请输入总数\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数单位\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutTotalUnit\" placeholder=\"请选择总数单位\">\r\n                  <el-option label=\"件\" value=\"件\"></el-option>\r\n                  <el-option label=\"支\" value=\"支\"></el-option>\r\n                  <el-option label=\"张\" value=\"张\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutProcessType\" placeholder=\"请选择加工类型\" filterable\r\n                  :filter-method=\"filterProcessType\">\r\n                  <el-option v-for=\"item in filteredProcessTypeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutHeatNo\" placeholder=\"请输入炉号\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutSteelGrade\" placeholder=\"请输入钢种\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"轴数\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutAxles\" placeholder=\"请选择轴数\">\r\n                  <el-option label=\"2\" value=\"2\"></el-option>\r\n                  <el-option label=\"3\" value=\"3\"></el-option>\r\n                  <el-option label=\"4\" value=\"4\"></el-option>\r\n                  <el-option label=\"5\" value=\"5\"></el-option>\r\n                  <el-option label=\"6轴标准\" value=\"6轴标准\"></el-option>\r\n                  <el-option label=\"6轴非标准\" value=\"6轴非标准\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"出库备注\">\r\n            <el-input type=\"textarea\" v-model=\"factoryConfirmForm.stockOutRemark\" placeholder=\"请输入出库备注\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <div class=\"btn-wrapper\">\r\n          <el-button type=\"primary\" @click=\"submitStockOutConfirm\" size=\"medium\" class=\"dispatch-btn\">确认出库</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只读的出库信息表单 -->\r\n      <div class=\"section-container\"\r\n        v-if=\"measureFlag == 1 && taskInfoForm.taskStatus > 2 && taskInfoForm.taskType !== 2 && taskInfoForm.isDirectSupply != 3\">\r\n        <div class=\"section-title\">出库信息</div>\r\n\r\n        <el-form :model=\"taskInfoForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input :value=\"taskInfoForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input :value=\"taskInfoForm.carNum\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"皮重(t)\">\r\n                <el-input :value=\"taskInfoForm.tare\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"planForm.planType == 3\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物料规格\">\r\n                <el-input :value=\"materialSpecs\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input :value=\"planForm.sourceCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input :value=\"planForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input :value=\"taskInfoForm.stockOutSpec1Length\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input :value=\"taskInfoForm.stockOutTotals\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"taskInfoForm.taskType == 2\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-input :value=\"taskInfoForm.stockOutProcessType\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input :value=\"taskInfoForm.stockOutHeatNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input :value=\"taskInfoForm.stockOutSteelGrade\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"轴数\">\r\n                <el-input :value=\"taskInfoForm.stockOutAxles\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"备注\">\r\n            <el-input type=\"textarea\" :value=\"taskInfoForm.stockOutRemark\" disabled></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 可编辑的入库信息表单 -->\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 7\">\r\n        <div class=\"section-title\">入库信息</div>\r\n\r\n        <el-form :model=\"factoryConfirmForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input v-model=\"factoryConfirmForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input v-model=\"factoryConfirmForm.carNum\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <!-- <div\r\n                v-if=\"taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input v-model=\"factoryConfirmForm.secGross\" placeholder=\"\" disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div v-if=\"taskInfoForm.isDirectSupply == 1\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input v-model=\"factoryConfirmForm.gross\" placeholder=\"\" disabled></el-input>\r\n                </el-form-item>\r\n              </div> -->\r\n\r\n              <el-form-item label=\"毛重(t)\">\r\n                <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.sourceCompany\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-select v-model=\"factoryConfirmForm.processType\" placeholder=\"请选择加工类型\" filterable\r\n                  :filter-method=\"filterProcessType\">\r\n                  <el-option v-for=\"item in filteredProcessTypeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input v-model=\"factoryConfirmForm.steelGrade\" placeholder=\"请输入钢种\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input v-model=\"factoryConfirmForm.spec1Length\" placeholder=\"请输入规格\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input v-model=\"factoryConfirmForm.total\" placeholder=\"请输入总数\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数单位\">\r\n                <el-select v-model=\"factoryConfirmForm.totalUnit\" placeholder=\"请选择总数单位\">\r\n                  <el-option label=\"件\" value=\"件\"></el-option>\r\n                  <el-option label=\"支\" value=\"支\"></el-option>\r\n                  <el-option label=\"张\" value=\"张\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input v-model=\"factoryConfirmForm.heatNo\" placeholder=\"请输入炉号/批号\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"扣重(t)\">\r\n                <el-input v-model=\"factoryConfirmForm.deductWeight\" placeholder=\"请输入扣重\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"出库备注\">\r\n            <el-input type=\"textarea\" v-model=\"factoryConfirmForm.remark\" placeholder=\"请输入出库备注\"></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"是否直供\" v-if=\"taskInfoForm.taskType == 2\">\r\n            <el-checkbox v-model=\"factoryConfirmForm.showDropdown\" @change=\"handleShowDropdownChange\">是否直供</el-checkbox>\r\n          </el-form-item>\r\n\r\n          <el-form-item v-if=\"factoryConfirmForm.showDropdown\" label=\"直供申请单号\">\r\n            <el-input v-model=\"factoryConfirmForm.extraOption\" placeholder=\"请选择直供申请单号\" readonly style=\"width: 300px;\">\r\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openOptionDialog\"></el-button>\r\n            </el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <div class=\"btn-wrapper\">\r\n          <el-button type=\"primary\" @click=\"submitFactoryConfirm\" size=\"medium\" class=\"dispatch-btn\">确认入库</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只读的入库信息表单 -->\r\n      <div class=\"section-container\"\r\n        v-if=\"measureFlag == 1 && taskInfoForm.taskStatus > 7 && taskInfoForm.taskType !== 1\">\r\n        <div class=\"section-title\">入库信息</div>\r\n\r\n        <el-form :model=\"taskInfoForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input :value=\"taskInfoForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input :value=\"taskInfoForm.carNum\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <!-- <div\r\n                v-if=\"taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div v-if=\"taskInfoForm.isDirectSupply == 1\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input :value=\"taskInfoForm.gross\" disabled></el-input>\r\n                </el-form-item>\r\n              </div> -->\r\n\r\n              <el-form-item label=\"毛重(t)\">\r\n                <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input :value=\"planForm.sourceCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input :value=\"planForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-input :value=\"taskInfoForm.processType\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input :value=\"taskInfoForm.steelGrade\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input :value=\"taskInfoForm.spec1Length\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input :value=\"taskInfoForm.totals\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input :value=\"taskInfoForm.heatNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"扣重\">\r\n                <el-input :value=\"taskInfoForm.deductWeight\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"备注\">\r\n            <el-input type=\"textarea\" :value=\"taskInfoForm.remark\" disabled></el-input>\r\n          </el-form-item>\r\n\r\n\r\n          <el-form-item v-if=\"taskInfoForm.directSupplyTaskNo\" label=\"对应申请单号\">\r\n            <el-input :value=\"taskInfoForm.directSupplyTaskNo\" disabled style=\"width: 300px;\"></el-input>\r\n            <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n              @click=\"openNewTaskWindow\">直供对应任务号</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 日志列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">任务日志</div>\r\n        <el-timeline>\r\n          <el-timeline-item v-for=\"(log, index) in taskLogs\" :key=\"index\" :timestamp=\"log.createTime\"\r\n            :color=\"getLogColor(log)\">\r\n            {{ log.info }}\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n\r\n      <div class=\"form-footer\">\r\n        <el-button @click=\"cancel\">返 回</el-button>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 选项弹窗 -->\r\n    <el-dialog title=\"选择直供申请单号\" :visible.sync=\"optionDialogVisible\" width=\"1600px\">\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"计划号\">\r\n          <el-input v-model=\"searchForm.planNo\" placeholder=\"请输入计划号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"申请编号\">\r\n          <el-input v-model=\"searchForm.applyNo\" placeholder=\"请输入申请编号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"收货单位\">\r\n          <el-input v-model=\"searchForm.receiveCompany\" placeholder=\"请输入收货单位\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"searchOptions\">查询</el-button>\r\n          <el-button @click=\"resetSearch\">重置</el-button>\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewWindow\">直供对应任务号\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-table :data=\"optionList\" style=\"width: 100%\" @selection-change=\"handleOptionSelection\" ref=\"optionTable\">\r\n        <el-table-column type=\"selection\" width=\"55\" />\r\n        <el-table-column prop=\"planNo\" label=\"计划号\" width=\"150\" />\r\n        <el-table-column prop=\"applyNo\" label=\"申请编号\" width=\"150\" />\r\n        <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"150\" />\r\n        <el-table-column prop=\"materialSpec\" label=\"物料规格\" width=\"120\" />\r\n        <el-table-column prop=\"sourceCompany\" label=\"申请单位\" width=\"150\" />\r\n        <el-table-column prop=\"receiveCompany\" label=\"收货单位\" width=\"150\" />\r\n        <el-table-column prop=\"plannedAmount\" label=\"计划量/t\" width=\"150\" />\r\n        <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"planStatus\" label=\"状态\" width=\"150\" />\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"optionDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmOptionSelection\">确认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getTask, getTaskmaterials, getProcessList, getDirectSupplyPlans, getDirectSupplyPlanAndTaskDetail, handleUnload, handleStockOut, isAllowDispatch, getPlanMaterials, editTaskmaterials, getTaskLogs, addLeaveLog, updateTask, addLeaveLogAndEditTaskMaterialsAndUpdateTask } from \"@/api/leave/task\";\r\nimport { detailPlan } from \"@/api/leave/plan\";\r\nimport { listXctgDriverCar, getXctgDriverCar, delXctgDriverCar, addXctgDriverCar, updateXctgDriverCar, exportXctgDriverCar } from \"@/api/truck/common/xctgDriverCar\";\r\nimport { Message } from \"element-ui\";\r\nimport QRCode from \"qrcodejs2\";\r\n\r\n\r\nexport default {\r\n  name: \"DispatchTaskDetail\",\r\n  data() {\r\n    return {\r\n      factoryConfirmDialogVisible: false,\r\n      factoryConfirmForm: {\r\n        companyName: '',\r\n        taskNo: '',\r\n        applyNo: '',\r\n        planNo: '',\r\n        taskType: null,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: null,\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9, // 完成状态\r\n        carNum: '', // 车牌号\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        handledMaterialName: '',\r\n        sourceCompany: '',\r\n        receiveCompany: '',\r\n        showDropdown: false,\r\n        extraOption: '',\r\n        deductWeight: null, // 添加扣重字段\r\n      },\r\n      optionDialogVisible: false,\r\n      searchForm: {\r\n        planNo: '',\r\n        applyNo: '',\r\n        receiveCompany: ''\r\n      },\r\n      optionList: [],\r\n      editDoorManStatus: false,\r\n      editFactoryStatus: false,\r\n      // 司机信息\r\n      driverInfo: {\r\n        id: 1,\r\n        name: '王小明',\r\n        idCard: '110101199001010001',\r\n        phone: '13800138000',\r\n        gender: '1',\r\n        company: '北京运输有限公司',\r\n        photo: 'https://via.placeholder.com/150',\r\n        driverLicenseImgs: 'https://via.placeholder.com/300x200',\r\n        vehicleLicenseImgs: 'https://via.placeholder.com/300x200'\r\n      },\r\n\r\n      // 车辆信息\r\n      carInfo: {},\r\n\r\n      // 任务物资列表\r\n      taskMaterials: [],\r\n\r\n      // 任务日志列表\r\n      taskLogs: [],\r\n\r\n      // 申请编号\r\n      applyNo: null,\r\n\r\n      isdoorMan: false,\r\n\r\n      // 派车任务ID\r\n      dispatchId: null,\r\n\r\n      taskInfoForm: {},\r\n\r\n      measureFlag: null,\r\n\r\n      backupTaskMaterials: null,\r\n      taskNo: null,\r\n\r\n      selectedOption: null,\r\n\r\n      planForm: {},\r\n\r\n      processTypeOptions: [], // 动态加载的加工类型选项\r\n\r\n      filteredProcessTypeOptions: [], // 过滤后的加工类型选项\r\n\r\n      searchProcessTypeQuery: '',// 搜索框的值\r\n\r\n      directSupplyPlanList: [], // 直供计划列表\r\n\r\n      editingRow: null,\r\n\r\n      selectedRows: [], // 添加选中行数据数组\r\n\r\n      directSupplyParams: {}\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    displayProcessTypeOptions() {\r\n      return this.searchProcessTypeQuery ? this.filteredProcessTypeOptions : this.processTypeOptions;\r\n    },\r\n\r\n    // 是否有选中的项\r\n    hasSelectedItems() {\r\n      return this.selectedRows.length > 0;\r\n    },\r\n\r\n    // 添加计算属性\r\n    materialNames() {\r\n      return this.taskMaterials.map(item => item.materialName).join(' ');\r\n    },\r\n\r\n    materialSpecs() {\r\n      return this.taskMaterials.map(item => item.materialSpec).join(' ');\r\n    }\r\n  },\r\n\r\n  activated() {\r\n    console.log(\"activated执行\");\r\n    this.resetTaskInfoForm();\r\n    // 获取路由参数\r\n    const { dispatchId, applyNo, measureFlag, planType, taskNo } = this.$route.query;\r\n    this.dispatchId = dispatchId;\r\n    this.applyNo = applyNo;\r\n    this.measureFlag = measureFlag;\r\n    console.log(\"this.measureFlag\", this.measureFlag)\r\n    this.planType = planType;\r\n    this.taskNo = taskNo;\r\n    console.log(\"taskNo\", this.taskNo);\r\n    this.validDoorMan();\r\n\r\n    // 使用 async/await 确保按顺序执行\r\n    this.initializeData();\r\n\r\n  },\r\n\r\n  methods: {\r\n    getDirectSupplyPlanAndTask() {\r\n\r\n\r\n      let leaveTask0 = {\r\n        taskNo: this.taskInfoForm.directSupplyTaskNo\r\n      }\r\n\r\n      getDirectSupplyPlanAndTaskDetail(leaveTask0).then(res => {\r\n        console.log(\"getDirectSupplyPlanAndTaskDetail\", res)\r\n        if (res.code == 200) {\r\n          this.directSupplyParams.dispatchId = res.rows[0].id;\r\n          this.directSupplyParams.applyNo = res.rows[0].applyNo;\r\n          this.directSupplyParams.taskNo = res.rows[0].taskNo;\r\n          this.directSupplyParams.measureFlag = res.rows[1].measureFlag;\r\n          this.directSupplyParams.planType = res.rows[1].planType;\r\n        } else {\r\n          this.$message.error(res.message || '获取计划列表失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('getDirectSupplyPlanAndTaskDetail error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n        throw err;\r\n      });\r\n\r\n    },\r\n\r\n    validDoorMan() {\r\n      this.$store.getters.roles.forEach(item => {\r\n        if (item == 'leave.quard') {\r\n          this.isdoorMan = true;\r\n        }\r\n      });\r\n      console.log(\"isdoorMan\", this.isdoorMan)\r\n    },\r\n    async initializeData() {\r\n      try {\r\n        // 等待所有异步操作完成\r\n        await this.getTaskInfo();\r\n        await this.getTaskmaterialList(this.taskNo);\r\n        await this.getPlanInfo(this.applyNo);\r\n\r\n        // 在所有数据加载完成后执行\r\n        this.uploadFactoryConfirmForm();\r\n\r\n        // 其他初始化操作\r\n        this.getTaskLogList(this.taskNo);\r\n        this.getProcessType();\r\n\r\n        //查询直供对应计划、任务详情\r\n        this.getDirectSupplyPlanAndTask();\r\n      } catch (error) {\r\n        console.error('Error initializing data:', error);\r\n        this.$message.error('数据加载失败，请刷新页面重试');\r\n      }\r\n    },\r\n\r\n    uploadFactoryConfirmForm() {\r\n      // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn\r\n      this.taskMaterials.forEach(item => {\r\n        item.doormanReceiveNum = item.planNum;\r\n        console.log(\"item.planType\", this.planForm.planType);\r\n        if (this.planForm.planType == 2 || this.planForm.planType == 3) {\r\n          item.doormanReceiveNumIn = item.planNum;\r\n        }\r\n      });\r\n\r\n      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');\r\n      let materialSpecs = this.taskMaterials.map(item => item.materialSpec).join(' ');\r\n      // 初始化表单数据\r\n      this.factoryConfirmForm = {\r\n        companyName: this.taskInfoForm.companyName,\r\n        gross: this.taskInfoForm.gross,\r\n        secGross: this.taskInfoForm.secGross,\r\n        driverName: this.taskInfoForm.driverName,\r\n        tare: this.taskInfoForm.tare,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        planNo: this.taskInfoForm.planNo,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: new Date(),\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9,\r\n        carNum: this.taskInfoForm.carNum, // 初始化车牌号\r\n        handledMaterialName: handledMaterialName,\r\n        materialSpecs: materialSpecs,\r\n        sourceCompany: this.planForm.sourceCompany,\r\n        receiveCompany: this.planForm.receiveCompany,\r\n        showDropdown: false, // 是否启用额外选项\r\n        extraOption: '', // 额外选项的值\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        deductWeight: null, // 添加扣重字段初始化\r\n      };\r\n    },\r\n\r\n    openNewWindow() {\r\n      const newWindowUrl = 'http://localhost/leave/leavePlanList'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    //获取可以直供的计划\r\n    async getDirectSupplyList() {\r\n      try {\r\n        let leavePlan = {\r\n          sourceCompany: this.planForm.sourceCompany,\r\n          planType: 3,\r\n        }\r\n        console.log(\"获取可以直供的计划\", leavePlan)\r\n\r\n        const res = await getDirectSupplyPlans(leavePlan);\r\n        console.log(\"getDirectSupplyPlans\", res)\r\n        if (res.code == 200) {\r\n          this.directSupplyPlanList = res.rows;\r\n          //查询每个计划的物资\r\n          for (const item of this.directSupplyPlanList) {\r\n            console.log(\"item\", item)\r\n            let leavePlanMaterial = {\r\n              applyNo: item.applyNo\r\n            };\r\n            const response = await getPlanMaterials(leavePlanMaterial);\r\n            if (response.code == 200) {\r\n              console.log(\"getPlanMaterials\", response)\r\n              item.materialName = response.rows[0].materialName;\r\n              item.materialSpec = response.rows[0].materialSpec;\r\n            } else {\r\n              this.$message.error(response.message || '获取计划物资失败');\r\n            }\r\n          }\r\n        } else {\r\n          this.$message.error(res.message || '获取计划列表失败');\r\n        }\r\n      } catch (err) {\r\n        console.error('getDirectSupplyPlans error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n        throw err;\r\n      }\r\n    },\r\n    filterProcessType(query) {\r\n      this.searchProcessTypeQuery = query;\r\n\r\n      if (this.searchProcessTypeQuery) {\r\n        console.log(\"processTypeOptions\", this.processTypeOptions)\r\n\r\n        this.filteredProcessTypeOptions = this.processTypeOptions.filter(item =>\r\n          item.value.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredProcessTypeOptions = this.processTypeOptions;\r\n      }\r\n    },\r\n    getProcessType() {\r\n      getProcessList().then(res => {\r\n        console.log(\"getProcessList\", res)\r\n        if (res.code == 200) {\r\n          this.processTypeOptions = res.rows.map(item => ({\r\n            value: item.processname,\r\n            label: item.processname\r\n          }));\r\n          this.filteredProcessTypeOptions = this.processTypeOptions; // 初始化过滤后的选项\r\n        } else {\r\n          this.$message.error(res.message || '获取加工类型失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('getProcessList error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n    async getPlanInfo(applyNo) {\r\n      try {\r\n        const response = await detailPlan(applyNo);\r\n        console.log(\"detailPlan\", response);\r\n        this.planForm = response.data;\r\n        await this.getDirectSupplyList();\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getPlanInfo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    openFactoryConfirmDialog() {\r\n      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');\r\n      // 初始化表单数据\r\n      this.factoryConfirmForm = {\r\n        companyName: this.taskInfoForm.companyName,\r\n        gross: this.taskInfoForm.gross,\r\n        secGross: this.taskInfoForm.secGross,\r\n        tare: this.taskInfoForm.tare,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        planNo: this.taskInfoForm.planNo,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: new Date(),\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9,\r\n        carNum: this.taskInfoForm.carNum, // 初始化车牌号\r\n        handledMaterialName: handledMaterialName,\r\n        sourceCompany: this.planForm.sourceCompany,\r\n        receiveCompany: this.planForm.receiveCompany,\r\n        showDropdown: false, // 是否启用额外选项\r\n        extraOption: '', // 额外选项的值\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        deductWeight: null, // 添加扣重字段初始化\r\n      };\r\n      this.factoryConfirmDialogVisible = true;\r\n    },\r\n    submitFactoryConfirm() {\r\n      if (this.factoryConfirmForm.showDropdown == true) {\r\n        if (this.factoryConfirmForm.extraOption == null || this.factoryConfirmForm.extraOption == '') {\r\n          this.$message.error('请选择额外选项');\r\n          return;\r\n        }\r\n      }\r\n\r\n      let submitData = {};\r\n      if (this.taskInfoForm.isDirectSupply == 3) {\r\n        // 构建提交数据\r\n        submitData = {\r\n          leaveTask: {\r\n            id: this.dispatchId,\r\n            taskNo: this.taskNo,\r\n            applyNo: this.applyNo,\r\n            //入库信息\r\n            spec1Length: this.factoryConfirmForm.spec1Length,\r\n            spec2Width: this.factoryConfirmForm.spec2Width,\r\n            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,\r\n            processType: this.factoryConfirmForm.processType,\r\n            heatNo: this.factoryConfirmForm.heatNo,\r\n            steelGrade: this.factoryConfirmForm.steelGrade,\r\n            axles: this.factoryConfirmForm.axles,\r\n            remark: this.factoryConfirmForm.remark,\r\n            carNum: this.taskInfoForm.carNum,\r\n            driverName: this.taskInfoForm.driverName,\r\n            isDirectSupply:3,\r\n            \r\n            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段\r\n            \r\n            // 出库信息\r\n            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n            stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n            stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n            // 更改任务状态: 9\r\n            // todo 任务状态如何变化\r\n            taskStatus: 8,\r\n            taskType: this.taskInfoForm.taskType,\r\n          },\r\n          leavePlan: this.planForm,\r\n          leaveTaskMaterial: this.taskMaterials[0],\r\n        };\r\n      } else {\r\n        // 构建提交数据\r\n        submitData = {\r\n          leaveTask: {\r\n            id: this.dispatchId,\r\n            taskNo: this.taskNo,\r\n            applyNo: this.applyNo,\r\n            //入库信息\r\n            spec1Length: this.factoryConfirmForm.spec1Length,\r\n            spec2Width: this.factoryConfirmForm.spec2Width,\r\n            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,\r\n            processType: this.factoryConfirmForm.processType,\r\n            heatNo: this.factoryConfirmForm.heatNo,\r\n            steelGrade: this.factoryConfirmForm.steelGrade,\r\n            axles: this.factoryConfirmForm.axles,\r\n            remark: this.factoryConfirmForm.remark,\r\n            carNum: this.taskInfoForm.carNum,\r\n            driverName: this.taskInfoForm.driverName,\r\n            isDirectSupply: 0, // 默认不是直供\r\n            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段\r\n            directSupplyTaskNo: this.factoryConfirmForm.extraOption,\r\n            // 出库信息\r\n            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n            stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n            stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n            // 更改任务状态: 9\r\n            // todo 任务状态如何变化\r\n            taskStatus: 8,\r\n            taskType: this.taskInfoForm.taskType,\r\n          },\r\n          leavePlan: this.planForm,\r\n          leaveTaskMaterial: this.taskMaterials[0],\r\n        };\r\n      }\r\n\r\n\r\n\r\n      let directSupplyTask = {\r\n        //taskNo后台雪花生成\r\n        applyNo: this.factoryConfirmForm.extraOption,\r\n        taskType: 3,\r\n        taskStatus: 7,\r\n        secGross: this.taskInfoForm.secGross,\r\n        secGrossTime: this.taskInfoForm.secGrossTime,\r\n        planNo: this.taskInfoForm.planNo,\r\n        driverName: this.taskInfoForm.driverName,\r\n        sex: this.taskInfoForm.sex,\r\n        mobilePhone: this.taskInfoForm.mobilePhone,\r\n        idCardNo: this.taskInfoForm.idCardNo,\r\n        carNum: this.taskInfoForm.carNum,\r\n        vehicleEmissionStandards: this.taskInfoForm.vehicleEmissionStandards,\r\n        faceImg: this.taskInfoForm.faceImg,\r\n        drivingLicenseImg: this.taskInfoForm.drivingLicenseImg,\r\n        driverLicenseImg: this.taskInfoForm.driverLicenseImg,\r\n        companyName: this.taskInfoForm.companyName,\r\n        isDirectSupply:3\r\n      };\r\n\r\n      let directSupplyTaskMaterialList = this.taskMaterials;\r\n\r\n      if (this.factoryConfirmForm.showDropdown == true && this.factoryConfirmForm.extraOption != null && this.factoryConfirmForm.extraOption != '') {\r\n        submitData.leaveTask.isDirectSupply = 1; // 设置为直供\r\n        submitData.directSupplyTask = directSupplyTask;\r\n        submitData.directSupplyTaskMaterialList = directSupplyTaskMaterialList;\r\n      }\r\n\r\n      handleUnload(submitData).then(res => {\r\n        console.log(\"handleUnload\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('确认入库成功');\r\n          this.factoryConfirmDialogVisible = false;\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '确认入库失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDirectSupply error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n    submitStockOutConfirm() {\r\n\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.unloading')) {\r\n        this.$message.error('您没有确认出库权限');\r\n        return;\r\n      }\r\n      // 构建提交数据\r\n      let submitData = {\r\n        leaveTask: {\r\n          //todo 计量系统补充信息待完善\r\n          id: this.dispatchId,\r\n          taskNo: this.taskNo,\r\n          applyNo: this.applyNo,\r\n          // 出库信息\r\n          stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n          stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n          stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n          stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n          stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n          stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n          stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n          stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n          // 更改任务状态: 9\r\n          taskStatus: 3,\r\n          carNum: this.taskInfoForm.carNum,\r\n        },\r\n        leavePlan: this.planForm,\r\n        leaveTaskMaterial: this.taskMaterials[0],\r\n      };\r\n\r\n      handleStockOut(submitData).then(res => {\r\n        console.log(\"handleStockOut\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('确认出库成功');\r\n          this.factoryConfirmDialogVisible = false;\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '确认出库失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDirectSupply error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n    handleFactoryConfirm() {\r\n      if (this.editFactoryStatus) {\r\n        this.$message.warning('请先保存');\r\n        return\r\n      }\r\n\r\n\r\n      //todo\r\n      //生成派车日志\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '分厂确认数量';\r\n\r\n\r\n      let factoryTaskInfo = {}\r\n      //todo 出入场\r\n      factoryTaskInfo.id = this.taskInfoForm.id\r\n      factoryTaskInfo.unloadingWorkNo = '卸货人占位符'\r\n      factoryTaskInfo.unloadingTime = new Date()\r\n      factoryTaskInfo.taskStatus = 9\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = factoryTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('分厂确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '分厂确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleFactoryConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n\r\n    handleDoorManConfirm() {\r\n      if (this.editDoorManStatus) {\r\n        this.$message.warning('请先保存');\r\n        return\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫确认数量';\r\n\r\n\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = doorManTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      // this.taskMaterials.map(item => {\r\n      //   editTaskmaterials(item);\r\n      // })\r\n      //todo\r\n      // let leaveTaskLog = {};\r\n      // leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫确认数量';\r\n      // addLeaveLog(leaveTaskLog);\r\n      // this.getTaskLogList(this.taskNo);\r\n\r\n      // let doorManTaskInfo = {}\r\n      // doorManTaskInfo.id = this.taskInfoForm.id\r\n      // if (this.taskInfoForm.taskType == 1) {\r\n      //   doorManTaskInfo.taskStatus = 9\r\n      //   doorManTaskInfo.leaveTime = new Date()\r\n      //   //离厂大门\r\n      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n      //   doorManTaskInfo.taskStatus = 7\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n      //   doorManTaskInfo.taskStatus = 6\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n      //   doorManTaskInfo.taskStatus = 5\r\n      //   doorManTaskInfo.leaveTime = new Date()\r\n      //   //离厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n      //   doorManTaskInfo.taskStatus = 7\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n      //   doorManTaskInfo.taskStatus = 6\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // }\r\n      // updateTask(doorManTaskInfo);\r\n      // this.$message.success('门卫确认成功');\r\n\r\n      // setTimeout(() => {\r\n      //   this.getTaskInfo();\r\n      // }, 500)\r\n\r\n    },\r\n\r\n    handleDoorManMeasureConfirm() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      if (this.taskInfoForm.taskStatus == 4) {\r\n        leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n      } else {\r\n        leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n      }\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = doorManTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n      //todo\r\n\r\n    },\r\n    // 生成二维码\r\n    creatQrCode() {\r\n      if (this.taskInfoForm.qrCodeContent) {\r\n        this.$refs.qrCode.innerHTML = \"\";\r\n        var YSqrCode = new QRCode(this.$refs.qrCode, {\r\n          text: this.taskInfoForm.qrCodeContent, // 需要转换为二维码的内容\r\n          width: 120,\r\n          height: 120,\r\n          colorDark: \"#000000\",\r\n          colorLight: \"#ffffff\",\r\n          correctLevel: QRCode.CorrectLevel.H,\r\n        });\r\n      }\r\n    },\r\n    getTaskLogList(taskNo) {\r\n      let taskLog = {};\r\n      taskLog.taskNo = taskNo\r\n      getTaskLogs(taskLog).then(response => {\r\n        console.log(\"getTaskLogs\", response);\r\n        // this.taskLogs = response.rows;\r\n        let logs = response.rows || [];\r\n        // 找出包含\"任务完成\"的日志\r\n        const finishedLogs = logs.filter(log => log.info && log.info.includes('任务完成'));\r\n        const otherLogs = logs.filter(log => !(log.info && log.info.includes('任务完成')));\r\n        // 先放\"任务完成\"，再放其他\r\n        this.taskLogs = [...finishedLogs, ...otherLogs];\r\n      })\r\n\r\n    },\r\n    async getTaskmaterialList(taskNo) {\r\n      try {\r\n        console.log(\"getTaskmaterialList\");\r\n        let leaveMaterial = {};\r\n        leaveMaterial.taskNo = taskNo;\r\n        const response = await getTaskmaterials(leaveMaterial);\r\n        this.taskMaterials = response.rows;\r\n        // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn\r\n        this.taskMaterials.forEach(item => {\r\n          item.doormanReceiveNum = item.planNum;\r\n          console.log(\"item.planType\", this.planForm.planType);\r\n          if (this.planForm.planType == 2 || this.planForm.planType == 3) {\r\n            item.doormanReceiveNumIn = item.planNum;\r\n          }\r\n        });\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskmaterialList error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    editDoorManRow(row) {\r\n      row._backup = JSON.parse(JSON.stringify(row));//深拷贝\r\n      this.editingRow = row;\r\n      this.editDoorManStatus = true;\r\n      console.log(\"this.editDoorManRow\", row);\r\n    },\r\n    editFactoryRow() {\r\n      this.backupMaterials = JSON.parse(JSON.stringify(this.taskMaterials));//深拷贝\r\n      this.editFactoryStatus = true;\r\n    },\r\n    cancelDoorManEdit(row) {\r\n      //深拷贝\r\n      if (row._backup) {\r\n        // 恢复备份数据\r\n        Object.assign(row, row._backup);\r\n        delete row._backup; // 删除备份数据\r\n      };\r\n      this.editingRow = null; // 清空当前编辑行\r\n      this.editDoorManStatus = false;\r\n    },\r\n    cancelFactoryEdit() {\r\n      this.taskMaterials = JSON.parse(JSON.stringify(this.backupMaterials));//深拷贝\r\n      console.log(\"this.taskMaterials\", this.taskMaterials);\r\n      this.editFactoryStatus = false;\r\n    },\r\n\r\n    saveDoorManRowIn() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      if (this.taskMaterials.length == 0) {\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        this.$message.warning('物资异常');\r\n        return\r\n      }\r\n\r\n      // 校验doormanReceiveNumIn是否等于planNum\r\n      for (const item of this.taskMaterials) {\r\n        if (item.doormanReceiveNumIn !== item.planNum) {\r\n          this.$message.warning(`物资\"${item.materialName}\"的门卫入厂确认数量(${item.doormanReceiveNumIn})与计划数量(${item.planNum})不一致，请检查`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id;\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {\r\n        taskMaterialList: this.taskMaterials,\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: doorManTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", param, this.taskInfoForm.taskType);\r\n\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      this.editDoorManStatus = false;\r\n    },\r\n\r\n    saveDoorManRow() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      console.log(\"roles\", roles);\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      if (this.taskMaterials.length == 0) {\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        this.$message.warning('物资异常');\r\n        return\r\n      }\r\n\r\n      // 校验doormanReceiveNum是否等于planNum\r\n      for (const item of this.taskMaterials) {\r\n        if (item.doormanReceiveNum !== item.planNum) {\r\n          this.$message.warning(`物资\"${item.materialName}\"的门卫确认数量(${item.doormanReceiveNum})与计划数量(${item.planNum})不一致，请检查`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {\r\n        taskMaterialList: this.taskMaterials,\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: doorManTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", param, this.taskInfoForm.taskType);\r\n\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      this.editDoorManStatus = false;\r\n    },\r\n\r\n\r\n    saveFactoryRow() {\r\n\r\n      this.editFactoryStatus = false;\r\n    },\r\n\r\n    resetTaskInfoForm() {\r\n      this.taskInfoForm = {};\r\n    },\r\n\r\n    async getTaskInfo() {\r\n      try {\r\n        const response = await getTask(this.dispatchId);\r\n        this.taskInfoForm = response.data;\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        if (this.taskInfoForm.licensePlateColor == 1) {\r\n          this.taskInfoForm.licensePlateColor = '蓝色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 2) {\r\n          this.taskInfoForm.licensePlateColor = '绿色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 3) {\r\n          this.taskInfoForm.licensePlateColor = '黄'\r\n        } else if (this.taskInfoForm.licensePlateColor == 4) {\r\n          this.taskInfoForm.licensePlateColor = '黄绿色'\r\n        }\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        // 生成二维码\r\n        this.$nextTick(() => {\r\n          this.creatQrCode();\r\n        });\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskInfo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n\r\n    getStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待过皮重',\r\n        2: '待装货',\r\n        3: '待过毛重',\r\n        4: '待出厂',\r\n        5: '待返厂',\r\n        6: '待过毛重(复磅)',\r\n        7: '待卸货',\r\n        8: '待过皮重(复磅)',\r\n        9: '完成'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    //计划状态\r\n    getPlanStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待分厂审批',\r\n        2: '待分厂复审',\r\n        3: '待生产指挥中心审批',\r\n        4: '审批完成',\r\n        5: '已出厂',\r\n        6: '部分收货',\r\n        7: '已完成',\r\n        11: '驳回',\r\n        12: '废弃',\r\n        13: '过期',\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n    // 获取排放标准文本\r\n    getEmissionStandardsText(standard) {\r\n      const standardMap = {\r\n        1: '国五',\r\n        2: '国六',\r\n        3: '新能源'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    // 获取排放标准标签类型\r\n    getEmissionStandardsTagType(standard) {\r\n      const typeMap = {\r\n        1: 'warning',  // 国五\r\n        2: 'success',  // 国六\r\n        3: 'primary'   // 新能源\r\n      };\r\n      return typeMap[standard] || 'info';\r\n    },\r\n\r\n    // 获取物资状态文本\r\n    getMaterialStatusText(status) {\r\n      const statusMap = {\r\n        1: '待装载',\r\n        2: '已装载',\r\n        3: '已签收',\r\n        4: '异常'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取物资状态标签类型\r\n    getMaterialStatusType(status) {\r\n      const typeMap = {\r\n        1: 'info',     // 待装载\r\n        2: 'warning',  // 已装载\r\n        3: 'success',  // 已签收\r\n        4: 'danger'    // 异常\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    // 获取日志颜色\r\n    getLogColor(log) {\r\n      const logTypeColorMap = {\r\n        1: '#409EFF', // 创建\r\n        2: '#E6A23C', // 更新\r\n        3: '#67C23A', // 完成\r\n        4: '#F56C6C', // 异常\r\n        5: '#909399'  // 其他\r\n      };\r\n      return logTypeColorMap[log.type] || '#409EFF';\r\n    },\r\n\r\n    // 返回按钮\r\n    cancel() {\r\n      this.$router.go(-1);\r\n    },\r\n\r\n    // 获取任务详情数据\r\n    getTaskDetail(dispatchId) {\r\n      // 实际项目中这里需要调用API获取数据\r\n      // getDispatchTaskDetail(dispatchId).then(response => {\r\n      //   const { driverInfo, carInfo, taskMaterials, taskLogs } = response.data;\r\n      //   this.driverInfo = driverInfo;\r\n      //   this.carInfo = carInfo;\r\n      //   this.taskMaterials = taskMaterials;\r\n      //   this.taskLogs = taskLogs;\r\n      // });\r\n    },\r\n    handleShowDropdownChange(val) {\r\n      if (!val) {\r\n        this.factoryConfirmForm.extraOption = '';\r\n      }\r\n    },\r\n    openOptionDialog() {\r\n      this.optionDialogVisible = true;\r\n      this.loadOptions();\r\n      // 重置选中状态\r\n      this.selectedOption = null;\r\n      this.$nextTick(() => {\r\n        if (this.$refs.optionTable) {\r\n          this.$refs.optionTable.clearSelection();\r\n        }\r\n      });\r\n    },\r\n    handleOptionSelection(selection) {\r\n      // 只保留最后选中的一项\r\n      if (selection.length > 1) {\r\n        const lastSelected = selection[selection.length - 1];\r\n        this.$refs.optionTable.clearSelection();\r\n        this.$refs.optionTable.toggleRowSelection(lastSelected, true);\r\n        this.selectedOption = lastSelected;\r\n      } else {\r\n        this.selectedOption = selection[0];\r\n      }\r\n    },\r\n    confirmOptionSelection() {\r\n      if (!this.selectedOption) {\r\n        this.$message.warning('请选择一个选项');\r\n        return;\r\n      }\r\n\r\n      this.factoryConfirmForm.extraOption = this.selectedOption.applyNo;\r\n\r\n      // let dispatchInfo = {};\r\n      // dispatchInfo.carNum = this.taskInfoForm.carNum;\r\n      // dispatchInfo.isDirectSupply = 1;\r\n\r\n      // isAllowDispatch(dispatchInfo).then(response => {\r\n      //   let row = response.data;\r\n      //   if (row > 0) {\r\n      //     this.$message.error(\"当前车有正在执行的任务\")\r\n      //     return;\r\n      //   } else {\r\n      //     this.optionDialogVisible = false;\r\n      //     this.$message.success('选项已确认');\r\n      //   }\r\n      //   console.log(\"this.isAllowDispatch\", response);\r\n      // }).catch(err => {\r\n      //   console.error('dispatch error:', err);\r\n      //   this.$message.error('网络异常，稍后重试');\r\n      // });\r\n\r\n      this.optionDialogVisible = false;\r\n      this.$message.success('选项已确认');\r\n\r\n\r\n\r\n    },\r\n    loadOptions() {\r\n      // 这里应该调用API获取leave_plan表的数据\r\n      this.optionList = this.directSupplyPlanList; // 使用直供计划列表作为选项数据\\\r\n      this.optionList.forEach(item => {\r\n        item.planStatus = this.getPlanStatusText(item.planStatus);\r\n      });\r\n      console.log(\"optionList\", this.optionList)\r\n    },\r\n    getBusinessCategoryText(category) {\r\n      const categoryMap = {\r\n        1: '通用（出厂不返回）',\r\n        11: '通用（出厂返回）',\r\n        12: '委外加工（出厂返回）',\r\n        21: '有计划量计量（跨区调拨）',\r\n        22: '短期（跨区调拨）',\r\n        23: '钢板（圆钢）（跨区调拨）',\r\n        31: '通用（退货申请）'\r\n      };\r\n      return categoryMap[category] || '未知类型';\r\n    },\r\n    searchOptions() {\r\n      // 取出并转小写\r\n      const searchPlanNo = (this.searchForm.planNo || '').toLowerCase();\r\n      const searchApplyNo = (this.searchForm.applyNo || '').toLowerCase();\r\n      const searchReceiveCompany = (this.searchForm.receiveCompany || '').toLowerCase();\r\n\r\n      // 过滤\r\n      this.optionList = this.directSupplyPlanList.filter(item => {\r\n        const planNo = (item.planNo || '').toString().toLowerCase();\r\n        const applyNo = (item.applyNo || '').toString().toLowerCase();\r\n        const receiveCompany = (item.receiveCompany || '').toString().toLowerCase();\r\n\r\n        // 为空不作为条件\r\n        const matchPlanNo = !searchPlanNo || planNo.includes(searchPlanNo);\r\n        const matchApplyNo = !searchApplyNo || applyNo.includes(searchApplyNo);\r\n        const matchReceiveCompany = !searchReceiveCompany || receiveCompany.includes(searchReceiveCompany);\r\n\r\n        return matchPlanNo && matchApplyNo && matchReceiveCompany;\r\n      });\r\n\r\n      // 更新状态显示\r\n      this.optionList.forEach(item => {\r\n        item.planStatus = this.getPlanStatusText(item.planStatus);\r\n      });\r\n    },\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        planNo: '',\r\n        applyNo: '',\r\n        receiveCompany: ''\r\n      };\r\n      this.loadOptions(); // 重新加载所有数据\r\n    },\r\n    getTaskTypeText(type) {\r\n      const typeMap = {\r\n        1: '出厂',\r\n        2: '返厂',\r\n        3: '跨区调拨'\r\n      };\r\n      return typeMap[type] || '未知';\r\n    },\r\n    // // 判断行是否可选\r\n    // isSelectable(row) {\r\n    //   // 当门卫确认数量不为0时，该行可选\r\n    //   return row.doormanReceiveNum > 0 && this.taskInfoForm.taskStatus !== 9;\r\n    // },\r\n\r\n    // 表格选择变化时的处理函数\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n    },\r\n\r\n    // 处理非计量分厂确认\r\n    handleNonMeasureFactoryConfirm() {\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.unloading')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n      let isHandled = false;\r\n      this.selectedRows.forEach(item => {\r\n        if (item.doormanReceiveNum !== item.planNum) {\r\n          this.$message.warning('门卫确认数量和计划数量不一致，请检查');\r\n          isHandled = true;\r\n        }\r\n      });\r\n\r\n      if (isHandled) {\r\n        return;\r\n      }\r\n\r\n      // if (this.selectedRows.length === 0) {\r\n      //   this.$message.warning('请选择需要确认的物资');\r\n      //   return;\r\n      // }\r\n\r\n      // 生成派车日志\r\n      let leaveTaskLog = {\r\n        logType: 2,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        info: '分厂接收确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ')\r\n      };\r\n\r\n      // 构建任务信息\r\n      let factoryTaskInfo = {\r\n        id: this.taskInfoForm.id,\r\n        unloadingWorkNo: '卸货人占位符',//后端updateLeaveTask方法\r\n        unloadingTime: new Date(),\r\n        taskStatus: 9\r\n      };\r\n\r\n      this.selectedRows.forEach(item => {\r\n        // 设置非计量分厂确认数量\r\n        item.factoryReceiveNum = item.doormanReceiveNum;\r\n      });\r\n\r\n      // 构建请求参数\r\n      let param = {\r\n        taskMaterialList: this.selectedRows, // 使用选中的行数据\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: factoryTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      // 发送请求\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        if (res.code == 200) {\r\n          this.$message.success('非计量分厂确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n          // 清空选中状态\r\n          this.selectedRows = [];\r\n        } else {\r\n          this.$message.error(res.message || '非计量分厂确认失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleNonMeasureFactoryConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n    openNewTaskWindow() {\r\n      console.log(\"openNewTaskWindow\", this.directSupplyParams);\r\n      let dispatchId = this.directSupplyParams.dispatchId;\r\n      let applyNo = BigInt(this.directSupplyParams.applyNo);\r\n      let measureFlag = this.directSupplyParams.measureFlag;\r\n      let planType = this.directSupplyParams.planType;\r\n      let taskNo = BigInt(this.directSupplyParams.taskNo);\r\n      const url = `http://localhost/leave/plan/task?dispatchId=${dispatchId}&applyNo=${applyNo}&measureFlag=${measureFlag}&planType=${planType}&taskNo=${taskNo}`;\r\n      window.open(url, '_blank');\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.btn-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.dispatch-btn {\r\n  margin-left: 15px;\r\n}\r\n\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.qrcode-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n\r\n.qrcode {\r\n  margin: 0 auto;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.section-container {\r\n  margin-bottom: 30px;\r\n  border-radius: 8px;\r\n  background: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-container:nth-child(1) {\r\n  border-top: 4px solid #F56C6C;\r\n  /* 通行证二维码模块 - 红色 */\r\n}\r\n\r\n.section-container:nth-child(2) {\r\n  border-top: 4px solid #409EFF;\r\n  /* 司机信息模块 - 蓝色 */\r\n}\r\n\r\n.section-container:nth-child(3) {\r\n  border-top: 4px solid #67C23A;\r\n  /* 车辆信息模块 - 绿色 */\r\n}\r\n\r\n.section-container:nth-child(4) {\r\n  border-top: 4px solid #E6A23C;\r\n  /* 物资列表模块 - 橙色 */\r\n}\r\n\r\n.section-container:nth-child(5) {\r\n  border-top: 4px solid #909399;\r\n  /* 日志列表模块 - 灰色 */\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  padding: 15px 20px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: #fafafa;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n\r\n.section-title::before {\r\n  content: '';\r\n  width: 4px;\r\n  height: 16px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-radius: 2px;\r\n}\r\n\r\n.section-container:nth-child(1) .section-title {\r\n  color: #F56C6C;\r\n}\r\n\r\n.section-container:nth-child(2) .section-title {\r\n  color: #409EFF;\r\n}\r\n\r\n.section-container:nth-child(3) .section-title {\r\n  color: #67C23A;\r\n}\r\n\r\n.section-container:nth-child(4) .section-title {\r\n  color: #E6A23C;\r\n}\r\n\r\n.section-container:nth-child(5) .section-title {\r\n  color: #909399;\r\n}\r\n\r\n.section-container .el-descriptions,\r\n.section-container .el-table,\r\n.section-container .el-timeline {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.form-footer {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.driver-photos {\r\n  padding: 0 20px 20px;\r\n  display: flex;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.photo-item {\r\n  width: 300px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.photo-item h4 {\r\n  padding: 10px;\r\n  background: #f5f7fa;\r\n  margin: 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.photo-container {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.photo-container img {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  object-fit: contain;\r\n}\r\n\r\n.button-container {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.el-table {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  th {\r\n    background-color: #fafafa !important;\r\n    color: #606266;\r\n    font-weight: bold;\r\n  }\r\n\r\n  td {\r\n    padding: 12px 0;\r\n  }\r\n}\r\n\r\n\r\n\r\n.el-timeline {\r\n  padding: 20px !important;\r\n\r\n  .el-timeline-item__node {\r\n    width: 12px;\r\n    height: 12px;\r\n  }\r\n\r\n  .el-timeline-item__content {\r\n    padding: 0 0 0 25px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  .el-descriptions-item__label {\r\n    background-color: #fafafa;\r\n  }\r\n}\r\n\r\n.el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy1BA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAC,sBAAA,CAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAGA;EACAM,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,2BAAA;MACAC,kBAAA;QACAC,WAAA;QACAC,MAAA;QACAC,OAAA;QACAC,MAAA;QACAC,QAAA;QACAC,eAAA;QACAC,aAAA;QACAC,WAAA;QACAC,UAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QAAA;QACAC,MAAA;QAAA;QACA;QACAC,mBAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,cAAA;QACAC,mBAAA;QACAC,aAAA;QACAC,cAAA;QACAC,YAAA;QACAC,WAAA;QACAC,YAAA;MACA;MACAC,mBAAA;MACAC,UAAA;QACAjC,MAAA;QACAD,OAAA;QACA6B,cAAA;MACA;MACAM,UAAA;MACAC,iBAAA;MACAC,iBAAA;MACA;MACAC,UAAA;QACAC,EAAA;QACA7C,IAAA;QACA8C,MAAA;QACAC,KAAA;QACAC,MAAA;QACAC,OAAA;QACAC,KAAA;QACAC,iBAAA;QACAC,kBAAA;MACA;MAEA;MACAC,OAAA;MAEA;MACAC,aAAA;MAEA;MACAC,QAAA;MAEA;MACAjD,OAAA;MAEAkD,SAAA;MAEA;MACAC,UAAA;MAEAC,YAAA;MAEAC,WAAA;MAEAC,mBAAA;MACAvD,MAAA;MAEAwD,cAAA;MAEAC,QAAA;MAEAC,kBAAA;MAAA;;MAEAC,0BAAA;MAAA;;MAEAC,sBAAA;MAAA;;MAEAC,oBAAA;MAAA;;MAEAC,UAAA;MAEAC,YAAA;MAAA;;MAEAC,kBAAA;IACA;EACA;EAEAC,QAAA;IACAC,yBAAA,WAAAA,0BAAA;MACA,YAAAN,sBAAA,QAAAD,0BAAA,QAAAD,kBAAA;IACA;IAEA;IACAS,gBAAA,WAAAA,iBAAA;MACA,YAAAJ,YAAA,CAAAK,MAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAA;MACA,YAAApB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;IACA;IAEAC,aAAA,WAAAA,cAAA;MACA,YAAAzB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAI,YAAA;MAAA,GAAAF,IAAA;IACA;EACA;EAEAG,SAAA,WAAAA,UAAA;IACAC,OAAA,CAAAC,GAAA;IACA,KAAAC,iBAAA;IACA;IACA,IAAAC,kBAAA,QAAAC,MAAA,CAAAC,KAAA;MAAA9B,UAAA,GAAA4B,kBAAA,CAAA5B,UAAA;MAAAnD,OAAA,GAAA+E,kBAAA,CAAA/E,OAAA;MAAAqD,WAAA,GAAA0B,kBAAA,CAAA1B,WAAA;MAAA6B,QAAA,GAAAH,kBAAA,CAAAG,QAAA;MAAAnF,MAAA,GAAAgF,kBAAA,CAAAhF,MAAA;IACA,KAAAoD,UAAA,GAAAA,UAAA;IACA,KAAAnD,OAAA,GAAAA,OAAA;IACA,KAAAqD,WAAA,GAAAA,WAAA;IACAuB,OAAA,CAAAC,GAAA,0BAAAxB,WAAA;IACA,KAAA6B,QAAA,GAAAA,QAAA;IACA,KAAAnF,MAAA,GAAAA,MAAA;IACA6E,OAAA,CAAAC,GAAA,gBAAA9E,MAAA;IACA,KAAAoF,YAAA;;IAEA;IACA,KAAAC,cAAA;EAEA;EAEAC,OAAA;IACAC,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,KAAA;MAGA,IAAAC,UAAA;QACAzF,MAAA,OAAAqD,YAAA,CAAAqC;MACA;MAEA,IAAAC,sCAAA,EAAAF,UAAA,EAAAG,IAAA,WAAAC,GAAA;QACAhB,OAAA,CAAAC,GAAA,qCAAAe,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAN,KAAA,CAAAxB,kBAAA,CAAAZ,UAAA,GAAAyC,GAAA,CAAAE,IAAA,IAAAvD,EAAA;UACAgD,KAAA,CAAAxB,kBAAA,CAAA/D,OAAA,GAAA4F,GAAA,CAAAE,IAAA,IAAA9F,OAAA;UACAuF,KAAA,CAAAxB,kBAAA,CAAAhE,MAAA,GAAA6F,GAAA,CAAAE,IAAA,IAAA/F,MAAA;UACAwF,KAAA,CAAAxB,kBAAA,CAAAV,WAAA,GAAAuC,GAAA,CAAAE,IAAA,IAAAzC,WAAA;UACAkC,KAAA,CAAAxB,kBAAA,CAAAmB,QAAA,GAAAU,GAAA,CAAAE,IAAA,IAAAZ,QAAA;QACA;UACAK,KAAA,CAAAQ,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAvB,OAAA,CAAAoB,KAAA,4CAAAG,GAAA;QACAZ,KAAA,CAAAQ,QAAA,CAAAC,KAAA;QACA,MAAAG,GAAA;MACA;IAEA;IAEAhB,YAAA,WAAAA,aAAA;MAAA,IAAAiB,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,CAAAC,KAAA,CAAAC,OAAA,WAAAlC,IAAA;QACA,IAAAA,IAAA;UACA8B,MAAA,CAAAlD,SAAA;QACA;MACA;MACA0B,OAAA,CAAAC,GAAA,mBAAA3B,SAAA;IACA;IACAkC,cAAA,WAAAA,eAAA;MAAA,IAAAqB,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,EAAA;QAAA,WAAAH,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAGAT,MAAA,CAAAW,WAAA;YAAA;cAAAH,QAAA,CAAAC,CAAA;cAAA,OACAT,MAAA,CAAAY,mBAAA,CAAAZ,MAAA,CAAA1G,MAAA;YAAA;cAAAkH,QAAA,CAAAC,CAAA;cAAA,OACAT,MAAA,CAAAa,WAAA,CAAAb,MAAA,CAAAzG,OAAA;YAAA;cAEA;cACAyG,MAAA,CAAAc,wBAAA;;cAEA;cACAd,MAAA,CAAAe,cAAA,CAAAf,MAAA,CAAA1G,MAAA;cACA0G,MAAA,CAAAgB,cAAA;;cAEA;cACAhB,MAAA,CAAAnB,0BAAA;cAAA2B,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAS,CAAA;cAEA9C,OAAA,CAAAoB,KAAA,6BAAAe,EAAA;cACAN,MAAA,CAAAV,QAAA,CAAAC,KAAA;YAAA;cAAA,OAAAiB,QAAA,CAAAU,CAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IAEA;IAEAS,wBAAA,WAAAA,yBAAA;MAAA,IAAAK,MAAA;MACA;MACA,KAAA5E,aAAA,CAAAwD,OAAA,WAAAlC,IAAA;QACAA,IAAA,CAAAuD,iBAAA,GAAAvD,IAAA,CAAAwD,OAAA;QACAlD,OAAA,CAAAC,GAAA,kBAAA+C,MAAA,CAAApE,QAAA,CAAA0B,QAAA;QACA,IAAA0C,MAAA,CAAApE,QAAA,CAAA0B,QAAA,SAAA0C,MAAA,CAAApE,QAAA,CAAA0B,QAAA;UACAZ,IAAA,CAAAyD,mBAAA,GAAAzD,IAAA,CAAAwD,OAAA;QACA;MACA;MAEA,IAAAnG,mBAAA,QAAAqB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MACA,IAAAC,aAAA,QAAAzB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAI,YAAA;MAAA,GAAAF,IAAA;MACA;MACA,KAAA3E,kBAAA;QACAC,WAAA,OAAAsD,YAAA,CAAAtD,WAAA;QACAkI,KAAA,OAAA5E,YAAA,CAAA4E,KAAA;QACAC,QAAA,OAAA7E,YAAA,CAAA6E,QAAA;QACAC,UAAA,OAAA9E,YAAA,CAAA8E,UAAA;QACAC,IAAA,OAAA/E,YAAA,CAAA+E,IAAA;QACApI,MAAA,OAAAA,MAAA;QACAC,OAAA,OAAAA,OAAA;QACAC,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;QACAE,eAAA;QACAC,aAAA,MAAAgI,IAAA;QACA/H,WAAA;QACAC,UAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,MAAA,OAAAoC,YAAA,CAAApC,MAAA;QAAA;QACAW,mBAAA,EAAAA,mBAAA;QACA8C,aAAA,EAAAA,aAAA;QACA7C,aAAA,OAAA4B,QAAA,CAAA5B,aAAA;QACAC,cAAA,OAAA2B,QAAA,CAAA3B,cAAA;QACAC,YAAA;QAAA;QACAC,WAAA;QAAA;QACA;QACAd,mBAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,cAAA;QACAM,YAAA;MACA;IACA;IAEAqG,aAAA,WAAAA,cAAA;MACA,IAAAC,YAAA;MACAC,MAAA,CAAAC,IAAA,CAAAF,YAAA;IACA;IACA;IACAG,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAhC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA8B,SAAA;QAAA,IAAAC,SAAA,EAAAhD,GAAA,EAAAiD,SAAA,EAAAC,KAAA,EAAAxE,IAAA,EAAAyE,iBAAA,EAAAC,QAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,WAAAtC,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAmC,SAAA;UAAA,kBAAAA,SAAA,CAAAjC,CAAA;YAAA;cAAAiC,SAAA,CAAAhC,CAAA;cAEAyB,SAAA;gBACAhH,aAAA,EAAA8G,MAAA,CAAAlF,QAAA,CAAA5B,aAAA;gBACAsD,QAAA;cACA;cACAN,OAAA,CAAAC,GAAA,cAAA+D,SAAA;cAAAO,SAAA,CAAAjC,CAAA;cAAA,OAEA,IAAAkC,0BAAA,EAAAR,SAAA;YAAA;cAAAhD,GAAA,GAAAuD,SAAA,CAAAzB,CAAA;cACA9C,OAAA,CAAAC,GAAA,yBAAAe,GAAA;cAAA,MACAA,GAAA,CAAAC,IAAA;gBAAAsD,SAAA,CAAAjC,CAAA;gBAAA;cAAA;cACAwB,MAAA,CAAA9E,oBAAA,GAAAgC,GAAA,CAAAE,IAAA;cACA;cAAA+C,SAAA,OAAAQ,2BAAA,CAAA1C,OAAA,EACA+B,MAAA,CAAA9E,oBAAA;cAAAuF,SAAA,CAAAhC,CAAA;cAAA0B,SAAA,CAAAS,CAAA;YAAA;cAAA,KAAAR,KAAA,GAAAD,SAAA,CAAA3B,CAAA,IAAAqC,IAAA;gBAAAJ,SAAA,CAAAjC,CAAA;gBAAA;cAAA;cAAA5C,IAAA,GAAAwE,KAAA,CAAAU,KAAA;cACA5E,OAAA,CAAAC,GAAA,SAAAP,IAAA;cACAyE,iBAAA;gBACA/I,OAAA,EAAAsE,IAAA,CAAAtE;cACA;cAAAmJ,SAAA,CAAAjC,CAAA;cAAA,OACA,IAAAuC,sBAAA,EAAAV,iBAAA;YAAA;cAAAC,QAAA,GAAAG,SAAA,CAAAzB,CAAA;cACA,IAAAsB,QAAA,CAAAnD,IAAA;gBACAjB,OAAA,CAAAC,GAAA,qBAAAmE,QAAA;gBACA1E,IAAA,CAAAC,YAAA,GAAAyE,QAAA,CAAAlD,IAAA,IAAAvB,YAAA;gBACAD,IAAA,CAAAI,YAAA,GAAAsE,QAAA,CAAAlD,IAAA,IAAApB,YAAA;cACA;gBACAgE,MAAA,CAAA3C,QAAA,CAAAC,KAAA,CAAAgD,QAAA,CAAA/C,OAAA;cACA;YAAA;cAAAkD,SAAA,CAAAjC,CAAA;cAAA;YAAA;cAAAiC,SAAA,CAAAjC,CAAA;cAAA;YAAA;cAAAiC,SAAA,CAAAhC,CAAA;cAAA8B,GAAA,GAAAE,SAAA,CAAAzB,CAAA;cAAAmB,SAAA,CAAAa,CAAA,CAAAT,GAAA;YAAA;cAAAE,SAAA,CAAAhC,CAAA;cAAA0B,SAAA,CAAAc,CAAA;cAAA,OAAAR,SAAA,CAAAQ,CAAA;YAAA;cAAAR,SAAA,CAAAjC,CAAA;cAAA;YAAA;cAGAwB,MAAA,CAAA3C,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;YAAA;cAAAkD,SAAA,CAAAjC,CAAA;cAAA;YAAA;cAAAiC,SAAA,CAAAhC,CAAA;cAAA+B,GAAA,GAAAC,SAAA,CAAAzB,CAAA;cAGA9C,OAAA,CAAAoB,KAAA,gCAAAkD,GAAA;cACAR,MAAA,CAAA3C,QAAA,CAAAC,KAAA;cAAA,MAAAkD,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAxB,CAAA;UAAA;QAAA,GAAAgB,QAAA;MAAA;IAGA;IACAiB,iBAAA,WAAAA,kBAAA3E,KAAA;MACA,KAAAtB,sBAAA,GAAAsB,KAAA;MAEA,SAAAtB,sBAAA;QACAiB,OAAA,CAAAC,GAAA,4BAAApB,kBAAA;QAEA,KAAAC,0BAAA,QAAAD,kBAAA,CAAAoG,MAAA,WAAAvF,IAAA;UAAA,OACAA,IAAA,CAAAkF,KAAA,CAAAM,QAAA,CAAA7E,KAAA;QAAA,CACA;MACA;QAEA,KAAAvB,0BAAA,QAAAD,kBAAA;MACA;IACA;IACAgE,cAAA,WAAAA,eAAA;MAAA,IAAAsC,MAAA;MACA,IAAAC,oBAAA,IAAArE,IAAA,WAAAC,GAAA;QACAhB,OAAA,CAAAC,GAAA,mBAAAe,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAkE,MAAA,CAAAtG,kBAAA,GAAAmC,GAAA,CAAAE,IAAA,CAAAzB,GAAA,WAAAC,IAAA;YAAA;cACAkF,KAAA,EAAAlF,IAAA,CAAA2F,WAAA;cACAC,KAAA,EAAA5F,IAAA,CAAA2F;YACA;UAAA;UACAF,MAAA,CAAArG,0BAAA,GAAAqG,MAAA,CAAAtG,kBAAA;QACA;UACAsG,MAAA,CAAAhE,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAvB,OAAA,CAAAoB,KAAA,0BAAAG,GAAA;QACA4D,MAAA,CAAAhE,QAAA,CAAAC,KAAA;MACA;IACA;IACAsB,WAAA,WAAAA,YAAAtH,OAAA;MAAA,IAAAmK,MAAA;MAAA,WAAAzD,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAuD,SAAA;QAAA,IAAApB,QAAA,EAAAqB,GAAA;QAAA,WAAAzD,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAsD,SAAA;UAAA,kBAAAA,SAAA,CAAApD,CAAA;YAAA;cAAAoD,SAAA,CAAAnD,CAAA;cAAAmD,SAAA,CAAApD,CAAA;cAAA,OAEA,IAAAqD,gBAAA,EAAAvK,OAAA;YAAA;cAAAgJ,QAAA,GAAAsB,SAAA,CAAA5C,CAAA;cACA9C,OAAA,CAAAC,GAAA,eAAAmE,QAAA;cACAmB,MAAA,CAAA3G,QAAA,GAAAwF,QAAA,CAAArJ,IAAA;cAAA2K,SAAA,CAAApD,CAAA;cAAA,OACAiD,MAAA,CAAA1B,mBAAA;YAAA;cAAA,OAAA6B,SAAA,CAAA3C,CAAA,IACAqB,QAAA;YAAA;cAAAsB,SAAA,CAAAnD,CAAA;cAAAkD,GAAA,GAAAC,SAAA,CAAA5C,CAAA;cAEA9C,OAAA,CAAAoB,KAAA,uBAAAqE,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA3C,CAAA;UAAA;QAAA,GAAAyC,QAAA;MAAA;IAGA;IACAI,wBAAA,WAAAA,yBAAA;MACA,IAAA7I,mBAAA,QAAAqB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MACA;MACA,KAAA3E,kBAAA;QACAC,WAAA,OAAAsD,YAAA,CAAAtD,WAAA;QACAkI,KAAA,OAAA5E,YAAA,CAAA4E,KAAA;QACAC,QAAA,OAAA7E,YAAA,CAAA6E,QAAA;QACAE,IAAA,OAAA/E,YAAA,CAAA+E,IAAA;QACApI,MAAA,OAAAA,MAAA;QACAC,OAAA,OAAAA,OAAA;QACAC,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;QACAE,eAAA;QACAC,aAAA,MAAAgI,IAAA;QACA/H,WAAA;QACAC,UAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,MAAA,OAAAoC,YAAA,CAAApC,MAAA;QAAA;QACAW,mBAAA,EAAAA,mBAAA;QACAC,aAAA,OAAA4B,QAAA,CAAA5B,aAAA;QACAC,cAAA,OAAA2B,QAAA,CAAA3B,cAAA;QACAC,YAAA;QAAA;QACAC,WAAA;QAAA;QACA;QACAd,mBAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,cAAA;QACAM,YAAA;MACA;MACA,KAAApC,2BAAA;IACA;IACA6K,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAA7K,kBAAA,CAAAiC,YAAA;QACA,SAAAjC,kBAAA,CAAAkC,WAAA,iBAAAlC,kBAAA,CAAAkC,WAAA;UACA,KAAAgE,QAAA,CAAAC,KAAA;UACA;QACA;MACA;MAEA,IAAA2E,UAAA;MACA,SAAAvH,YAAA,CAAAwH,cAAA;QACA;QACAD,UAAA;UACAE,SAAA;YACAtI,EAAA,OAAAY,UAAA;YACApD,MAAA,OAAAA,MAAA;YACAC,OAAA,OAAAA,OAAA;YACA;YACAK,WAAA,OAAAR,kBAAA,CAAAQ,WAAA;YACAC,UAAA,OAAAT,kBAAA,CAAAS,UAAA;YACAC,MAAA,OAAAV,kBAAA,CAAAW,KAAA,QAAAX,kBAAA,CAAAY,SAAA;YACAC,WAAA,OAAAb,kBAAA,CAAAa,WAAA;YACAC,MAAA,OAAAd,kBAAA,CAAAc,MAAA;YACAC,UAAA,OAAAf,kBAAA,CAAAe,UAAA;YACAC,KAAA,OAAAhB,kBAAA,CAAAgB,KAAA;YACAC,MAAA,OAAAjB,kBAAA,CAAAiB,MAAA;YACAE,MAAA,OAAAoC,YAAA,CAAApC,MAAA;YACAkH,UAAA,OAAA9E,YAAA,CAAA8E,UAAA;YACA0C,cAAA;YAEA5I,YAAA,OAAAnC,kBAAA,CAAAmC,YAAA;YAAA;;YAEA;YACAf,mBAAA,OAAApB,kBAAA,CAAAoB,mBAAA;YACAC,kBAAA,OAAArB,kBAAA,CAAAqB,kBAAA;YACAC,cAAA,OAAAtB,kBAAA,CAAAwB,aAAA,QAAAxB,kBAAA,CAAAuB,iBAAA;YACAE,mBAAA,OAAAzB,kBAAA,CAAAyB,mBAAA;YACAC,cAAA,OAAA1B,kBAAA,CAAA0B,cAAA;YACAC,kBAAA,OAAA3B,kBAAA,CAAA2B,kBAAA;YACAC,aAAA,OAAA5B,kBAAA,CAAA4B,aAAA;YACAC,cAAA,OAAA7B,kBAAA,CAAA6B,cAAA;YACA;YACA;YACAX,UAAA;YACAb,QAAA,OAAAkD,YAAA,CAAAlD;UACA;UACA0I,SAAA,OAAApF,QAAA;UACAsH,iBAAA,OAAA9H,aAAA;QACA;MACA;QACA;QACA2H,UAAA;UACAE,SAAA;YACAtI,EAAA,OAAAY,UAAA;YACApD,MAAA,OAAAA,MAAA;YACAC,OAAA,OAAAA,OAAA;YACA;YACAK,WAAA,OAAAR,kBAAA,CAAAQ,WAAA;YACAC,UAAA,OAAAT,kBAAA,CAAAS,UAAA;YACAC,MAAA,OAAAV,kBAAA,CAAAW,KAAA,QAAAX,kBAAA,CAAAY,SAAA;YACAC,WAAA,OAAAb,kBAAA,CAAAa,WAAA;YACAC,MAAA,OAAAd,kBAAA,CAAAc,MAAA;YACAC,UAAA,OAAAf,kBAAA,CAAAe,UAAA;YACAC,KAAA,OAAAhB,kBAAA,CAAAgB,KAAA;YACAC,MAAA,OAAAjB,kBAAA,CAAAiB,MAAA;YACAE,MAAA,OAAAoC,YAAA,CAAApC,MAAA;YACAkH,UAAA,OAAA9E,YAAA,CAAA8E,UAAA;YACA0C,cAAA;YAAA;YACA5I,YAAA,OAAAnC,kBAAA,CAAAmC,YAAA;YAAA;YACAyD,kBAAA,OAAA5F,kBAAA,CAAAkC,WAAA;YACA;YACAd,mBAAA,OAAApB,kBAAA,CAAAoB,mBAAA;YACAC,kBAAA,OAAArB,kBAAA,CAAAqB,kBAAA;YACAC,cAAA,OAAAtB,kBAAA,CAAAwB,aAAA,QAAAxB,kBAAA,CAAAuB,iBAAA;YACAE,mBAAA,OAAAzB,kBAAA,CAAAyB,mBAAA;YACAC,cAAA,OAAA1B,kBAAA,CAAA0B,cAAA;YACAC,kBAAA,OAAA3B,kBAAA,CAAA2B,kBAAA;YACAC,aAAA,OAAA5B,kBAAA,CAAA4B,aAAA;YACAC,cAAA,OAAA7B,kBAAA,CAAA6B,cAAA;YACA;YACA;YACAX,UAAA;YACAb,QAAA,OAAAkD,YAAA,CAAAlD;UACA;UACA0I,SAAA,OAAApF,QAAA;UACAsH,iBAAA,OAAA9H,aAAA;QACA;MACA;MAIA,IAAA+H,gBAAA;QACA;QACA/K,OAAA,OAAAH,kBAAA,CAAAkC,WAAA;QACA7B,QAAA;QACAa,UAAA;QACAkH,QAAA,OAAA7E,YAAA,CAAA6E,QAAA;QACA+C,YAAA,OAAA5H,YAAA,CAAA4H,YAAA;QACA/K,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;QACAiI,UAAA,OAAA9E,YAAA,CAAA8E,UAAA;QACA+C,GAAA,OAAA7H,YAAA,CAAA6H,GAAA;QACAC,WAAA,OAAA9H,YAAA,CAAA8H,WAAA;QACAC,QAAA,OAAA/H,YAAA,CAAA+H,QAAA;QACAnK,MAAA,OAAAoC,YAAA,CAAApC,MAAA;QACAoK,wBAAA,OAAAhI,YAAA,CAAAgI,wBAAA;QACAC,OAAA,OAAAjI,YAAA,CAAAiI,OAAA;QACAC,iBAAA,OAAAlI,YAAA,CAAAkI,iBAAA;QACAC,gBAAA,OAAAnI,YAAA,CAAAmI,gBAAA;QACAzL,WAAA,OAAAsD,YAAA,CAAAtD,WAAA;QACA8K,cAAA;MACA;MAEA,IAAAY,4BAAA,QAAAxI,aAAA;MAEA,SAAAnD,kBAAA,CAAAiC,YAAA,iBAAAjC,kBAAA,CAAAkC,WAAA,iBAAAlC,kBAAA,CAAAkC,WAAA;QACA4I,UAAA,CAAAE,SAAA,CAAAD,cAAA;QACAD,UAAA,CAAAI,gBAAA,GAAAA,gBAAA;QACAJ,UAAA,CAAAa,4BAAA,GAAAA,4BAAA;MACA;MAEA,IAAAC,kBAAA,EAAAd,UAAA,EAAAhF,IAAA,WAAAC,GAAA;QACAhB,OAAA,CAAAC,GAAA,iBAAAe,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA6E,MAAA,CAAA3E,QAAA,CAAA2F,OAAA;UACAhB,MAAA,CAAA9K,2BAAA;UACA8K,MAAA,CAAAlD,cAAA,CAAAkD,MAAA,CAAA3K,MAAA;UACA2K,MAAA,CAAAtD,WAAA;QACA;UACA;UACAsD,MAAA,CAAA3E,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAvB,OAAA,CAAAoB,KAAA,8BAAAG,GAAA;QACAuE,MAAA,CAAA3E,QAAA,CAAAC,KAAA;MACA;IACA;IAEA2F,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MAEA;MACA,IAAArF,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAuD,QAAA;QACA,KAAA/D,QAAA,CAAAC,KAAA;QACA;MACA;MACA;MACA,IAAA2E,UAAA;QACAE,SAAA;UACA;UACAtI,EAAA,OAAAY,UAAA;UACApD,MAAA,OAAAA,MAAA;UACAC,OAAA,OAAAA,OAAA;UACA;UACAiB,mBAAA,OAAApB,kBAAA,CAAAoB,mBAAA;UACAC,kBAAA,OAAArB,kBAAA,CAAAqB,kBAAA;UACAC,cAAA,OAAAtB,kBAAA,CAAAwB,aAAA,QAAAxB,kBAAA,CAAAuB,iBAAA;UACAE,mBAAA,OAAAzB,kBAAA,CAAAyB,mBAAA;UACAC,cAAA,OAAA1B,kBAAA,CAAA0B,cAAA;UACAC,kBAAA,OAAA3B,kBAAA,CAAA2B,kBAAA;UACAC,aAAA,OAAA5B,kBAAA,CAAA4B,aAAA;UACAC,cAAA,OAAA7B,kBAAA,CAAA6B,cAAA;UACA;UACAX,UAAA;UACAC,MAAA,OAAAoC,YAAA,CAAApC;QACA;QACA4H,SAAA,OAAApF,QAAA;QACAsH,iBAAA,OAAA9H,aAAA;MACA;MAEA,IAAA6I,oBAAA,EAAAlB,UAAA,EAAAhF,IAAA,WAAAC,GAAA;QACAhB,OAAA,CAAAC,GAAA,mBAAAe,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA+F,MAAA,CAAA7F,QAAA,CAAA2F,OAAA;UACAE,MAAA,CAAAhM,2BAAA;UACAgM,MAAA,CAAApE,cAAA,CAAAoE,MAAA,CAAA7L,MAAA;UACA6L,MAAA,CAAAxE,WAAA;QACA;UACA;UACAwE,MAAA,CAAA7F,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAvB,OAAA,CAAAoB,KAAA,8BAAAG,GAAA;QACAyF,MAAA,CAAA7F,QAAA,CAAAC,KAAA;MACA;IACA;IAEA8F,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAA1J,iBAAA;QACA,KAAA0D,QAAA,CAAAiG,OAAA;QACA;MACA;;MAGA;MACA;MACA,IAAAC,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAlM,MAAA,QAAAA,MAAA;MACAkM,YAAA,CAAAjM,OAAA,QAAAA,OAAA;MACAiM,YAAA,CAAAE,IAAA;MAGA,IAAAC,eAAA;MACA;MACAA,eAAA,CAAA7J,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA6J,eAAA,CAAAjM,eAAA;MACAiM,eAAA,CAAAhM,aAAA,OAAAgI,IAAA;MACAgE,eAAA,CAAArL,UAAA;MAEA,IAAAsL,KAAA;MACAA,KAAA,CAAAC,gBAAA,QAAAtJ,aAAA;MACAqJ,KAAA,CAAAE,QAAA,GAAAN,YAAA;MACAI,KAAA,CAAAxB,SAAA,GAAAuB,eAAA;MACAC,KAAA,CAAAhJ,WAAA,QAAAA,WAAA;MAEA,IAAAmJ,kDAAA,EAAAH,KAAA,EAAA1G,IAAA,WAAAC,GAAA;QACAhB,OAAA,CAAAC,GAAA,iDAAAe,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAkG,MAAA,CAAAhG,QAAA,CAAA2F,OAAA;UACAK,MAAA,CAAAvE,cAAA,CAAAuE,MAAA,CAAAhM,MAAA;UACAgM,MAAA,CAAA3E,WAAA;QACA;UACA;UACA2E,MAAA,CAAAhG,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAvB,OAAA,CAAAoB,KAAA,gCAAAG,GAAA;QACA4F,MAAA,CAAAhG,QAAA,CAAAC,KAAA;MACA;IACA;IAGAyG,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAAtK,iBAAA;QACA,KAAA2D,QAAA,CAAAiG,OAAA;QACA;MACA;MAEA,IAAAC,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAlM,MAAA,QAAAA,MAAA;MACAkM,YAAA,CAAAjM,OAAA,QAAAA,OAAA;MACAiM,YAAA,CAAAE,IAAA;MAIA,IAAAQ,eAAA;MACAA,eAAA,CAAApK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACAyM,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAsJ,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAsJ,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACA4L,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA4L,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA4L,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAV,KAAA;MACAA,KAAA,CAAAC,gBAAA,QAAAtJ,aAAA;MACAqJ,KAAA,CAAAE,QAAA,GAAAN,YAAA;MACAI,KAAA,CAAAxB,SAAA,GAAA8B,eAAA;MACAN,KAAA,CAAAhJ,WAAA,QAAAA,WAAA;MAEA,IAAAmJ,kDAAA,EAAAH,KAAA,EAAA1G,IAAA,WAAAC,GAAA;QACAhB,OAAA,CAAAC,GAAA,iDAAAe,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA6G,MAAA,CAAA3G,QAAA,CAAA2F,OAAA;UACAgB,MAAA,CAAAlF,cAAA,CAAAkF,MAAA,CAAA3M,MAAA;UACA2M,MAAA,CAAAtF,WAAA;QACA;UACA;UACAsF,MAAA,CAAA3G,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAvB,OAAA,CAAAoB,KAAA,gCAAAG,GAAA;QACAuG,MAAA,CAAA3G,QAAA,CAAAC,KAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACAiG,YAAA,CAAAlM,MAAA,QAAAA,MAAA;MACAkM,YAAA,CAAAjM,OAAA,QAAAA,OAAA;MACAiM,YAAA,CAAAE,IAAA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;IAEA;IAEAc,2BAAA,WAAAA,4BAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAA3G,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAuD,QAAA;QACA,KAAA/D,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,IAAAiG,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAlM,MAAA,QAAAA,MAAA;MACAkM,YAAA,CAAAjM,OAAA,QAAAA,OAAA;MACA,SAAAoD,YAAA,CAAArC,UAAA;QACAkL,YAAA,CAAAE,IAAA,yBAAAnJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,YAAA;QAAA,GAAAC,IAAA;MACA;QACAyH,YAAA,CAAAE,IAAA,yBAAAnJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,YAAA;QAAA,GAAAC,IAAA;MACA;MAEA,IAAAmI,eAAA;MACAA,eAAA,CAAApK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACAyM,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAsJ,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAsJ,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACA4L,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA4L,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA4L,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAV,KAAA;MACAA,KAAA,CAAAC,gBAAA,QAAAtJ,aAAA;MACAqJ,KAAA,CAAAE,QAAA,GAAAN,YAAA;MACAI,KAAA,CAAAxB,SAAA,GAAA8B,eAAA;MACAN,KAAA,CAAAhJ,WAAA,QAAAA,WAAA;MAEA,IAAAmJ,kDAAA,EAAAH,KAAA,EAAA1G,IAAA,WAAAC,GAAA;QACAhB,OAAA,CAAAC,GAAA,iDAAAe,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAqH,OAAA,CAAAnH,QAAA,CAAA2F,OAAA;UACAwB,OAAA,CAAA1F,cAAA,CAAA0F,OAAA,CAAAnN,MAAA;UACAmN,OAAA,CAAA9F,WAAA;QACA;UACA;UACA8F,OAAA,CAAAnH,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAvB,OAAA,CAAAoB,KAAA,gCAAAG,GAAA;QACA+G,OAAA,CAAAnH,QAAA,CAAAC,KAAA;MACA;MACA;IAEA;IACA;IACAmH,WAAA,WAAAA,YAAA;MACA,SAAA/J,YAAA,CAAAgK,aAAA;QACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,SAAA;QACA,IAAAC,QAAA,OAAAC,iBAAA,MAAAJ,KAAA,CAAAC,MAAA;UACAI,IAAA,OAAAtK,YAAA,CAAAgK,aAAA;UAAA;UACAO,KAAA;UACAC,MAAA;UACAC,SAAA;UACAC,UAAA;UACAC,YAAA,EAAAN,iBAAA,CAAAO,YAAA,CAAAC;QACA;MACA;IACA;IACAzG,cAAA,WAAAA,eAAAzH,MAAA;MAAA,IAAAmO,OAAA;MACA,IAAAC,OAAA;MACAA,OAAA,CAAApO,MAAA,GAAAA,MAAA;MACA,IAAAqO,iBAAA,EAAAD,OAAA,EAAAxI,IAAA,WAAAqD,QAAA;QACApE,OAAA,CAAAC,GAAA,gBAAAmE,QAAA;QACA;QACA,IAAAqF,IAAA,GAAArF,QAAA,CAAAlD,IAAA;QACA;QACA,IAAAwI,YAAA,GAAAD,IAAA,CAAAxE,MAAA,WAAAhF,GAAA;UAAA,OAAAA,GAAA,CAAAsH,IAAA,IAAAtH,GAAA,CAAAsH,IAAA,CAAArC,QAAA;QAAA;QACA,IAAAyE,SAAA,GAAAF,IAAA,CAAAxE,MAAA,WAAAhF,GAAA;UAAA,SAAAA,GAAA,CAAAsH,IAAA,IAAAtH,GAAA,CAAAsH,IAAA,CAAArC,QAAA;QAAA;QACA;QACAoE,OAAA,CAAAjL,QAAA,MAAAuL,MAAA,KAAAC,mBAAA,CAAA9H,OAAA,EAAA2H,YAAA,OAAAG,mBAAA,CAAA9H,OAAA,EAAA4H,SAAA;MACA;IAEA;IACAlH,mBAAA,WAAAA,oBAAAtH,MAAA;MAAA,IAAA2O,OAAA;MAAA,WAAAhI,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA8H,SAAA;QAAA,IAAAC,aAAA,EAAA5F,QAAA,EAAA6F,GAAA;QAAA,WAAAjI,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAA8H,SAAA;UAAA,kBAAAA,SAAA,CAAA5H,CAAA;YAAA;cAAA4H,SAAA,CAAA3H,CAAA;cAEAvC,OAAA,CAAAC,GAAA;cACA+J,aAAA;cACAA,aAAA,CAAA7O,MAAA,GAAAA,MAAA;cAAA+O,SAAA,CAAA5H,CAAA;cAAA,OACA,IAAA6H,sBAAA,EAAAH,aAAA;YAAA;cAAA5F,QAAA,GAAA8F,SAAA,CAAApH,CAAA;cACAgH,OAAA,CAAA1L,aAAA,GAAAgG,QAAA,CAAAlD,IAAA;cACA;cACA4I,OAAA,CAAA1L,aAAA,CAAAwD,OAAA,WAAAlC,IAAA;gBACAA,IAAA,CAAAuD,iBAAA,GAAAvD,IAAA,CAAAwD,OAAA;gBACAlD,OAAA,CAAAC,GAAA,kBAAA6J,OAAA,CAAAlL,QAAA,CAAA0B,QAAA;gBACA,IAAAwJ,OAAA,CAAAlL,QAAA,CAAA0B,QAAA,SAAAwJ,OAAA,CAAAlL,QAAA,CAAA0B,QAAA;kBACAZ,IAAA,CAAAyD,mBAAA,GAAAzD,IAAA,CAAAwD,OAAA;gBACA;cACA;cACAlD,OAAA,CAAAC,GAAA,kBAAA6J,OAAA,CAAA1L,aAAA;cAAA,OAAA8L,SAAA,CAAAnH,CAAA,IACAqB,QAAA;YAAA;cAAA8F,SAAA,CAAA3H,CAAA;cAAA0H,GAAA,GAAAC,SAAA,CAAApH,CAAA;cAEA9C,OAAA,CAAAoB,KAAA,+BAAA6I,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAnH,CAAA;UAAA;QAAA,GAAAgH,QAAA;MAAA;IAGA;IACAK,cAAA,WAAAA,eAAAC,GAAA;MACAA,GAAA,CAAAC,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAJ,GAAA;MACA,KAAApL,UAAA,GAAAoL,GAAA;MACA,KAAA7M,iBAAA;MACAwC,OAAA,CAAAC,GAAA,wBAAAoK,GAAA;IACA;IACAK,cAAA,WAAAA,eAAA;MACA,KAAAC,eAAA,GAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAArM,aAAA;MACA,KAAAX,iBAAA;IACA;IACAmN,iBAAA,WAAAA,kBAAAP,GAAA;MACA;MACA,IAAAA,GAAA,CAAAC,OAAA;QACA;QACAO,MAAA,CAAAC,MAAA,CAAAT,GAAA,EAAAA,GAAA,CAAAC,OAAA;QACA,OAAAD,GAAA,CAAAC,OAAA;MACA;MAAA;MACA,KAAArL,UAAA;MACA,KAAAzB,iBAAA;IACA;IACAuN,iBAAA,WAAAA,kBAAA;MACA,KAAA3M,aAAA,GAAAmM,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAE,eAAA;MACA3K,OAAA,CAAAC,GAAA,4BAAA7B,aAAA;MACA,KAAAX,iBAAA;IACA;IAEAuN,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAAtJ,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAuD,QAAA;QACA,KAAA/D,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,SAAAhD,aAAA,CAAAmB,MAAA;QACAS,OAAA,CAAAC,GAAA,uBAAA7B,aAAA;QACA,KAAA+C,QAAA,CAAAiG,OAAA;QACA;MACA;;MAEA;MAAA,IAAA8D,UAAA,OAAAzG,2BAAA,CAAA1C,OAAA,EACA,KAAA3D,aAAA;QAAA+M,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAxG,CAAA,MAAAyG,MAAA,GAAAD,UAAA,CAAA5I,CAAA,IAAAqC,IAAA;UAAA,IAAAjF,IAAA,GAAAyL,MAAA,CAAAvG,KAAA;UACA,IAAAlF,IAAA,CAAAyD,mBAAA,KAAAzD,IAAA,CAAAwD,OAAA;YACA,KAAA/B,QAAA,CAAAiG,OAAA,kBAAAwC,MAAA,CAAAlK,IAAA,CAAAC,YAAA,+DAAAiK,MAAA,CAAAlK,IAAA,CAAAyD,mBAAA,sCAAAyG,MAAA,CAAAlK,IAAA,CAAAwD,OAAA;YACA;UACA;QACA;MAAA,SAAA3B,GAAA;QAAA2J,UAAA,CAAApG,CAAA,CAAAvD,GAAA;MAAA;QAAA2J,UAAA,CAAAnG,CAAA;MAAA;MAEA,IAAAsC,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAlM,MAAA,QAAAA,MAAA;MACAkM,YAAA,CAAAjM,OAAA,QAAAA,OAAA;MACAiM,YAAA,CAAAE,IAAA,yBAAAnJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MAEA,IAAAmI,eAAA;MACAA,eAAA,CAAApK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACAyM,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAsJ,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAsJ,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACA4L,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA4L,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA4L,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAV,KAAA;QACAC,gBAAA,OAAAtJ,aAAA;QACAuJ,QAAA,EAAAN,YAAA;QACApB,SAAA,EAAA8B,eAAA;QACAtJ,WAAA,OAAAA;MACA;MAEAuB,OAAA,CAAAC,GAAA,iDAAAwH,KAAA,OAAAjJ,YAAA,CAAAlD,QAAA;MAGA,IAAAsM,kDAAA,EAAAH,KAAA,EAAA1G,IAAA,WAAAC,GAAA;QACAhB,OAAA,CAAAC,GAAA,iDAAAe,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAgK,OAAA,CAAA9J,QAAA,CAAA2F,OAAA;UACAmE,OAAA,CAAArI,cAAA,CAAAqI,OAAA,CAAA9P,MAAA;UACA8P,OAAA,CAAAzI,WAAA;QACA;UACA;UACAyI,OAAA,CAAA9J,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAvB,OAAA,CAAAoB,KAAA,gCAAAG,GAAA;QACA0J,OAAA,CAAA9J,QAAA,CAAAC,KAAA;MACA;MAEA,KAAA5D,iBAAA;IACA;IAEA4N,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAA1J,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA3B,OAAA,CAAAC,GAAA,UAAA0B,KAAA;MACA,KAAAA,KAAA,CAAAuD,QAAA;QACA,KAAA/D,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,SAAAhD,aAAA,CAAAmB,MAAA;QACAS,OAAA,CAAAC,GAAA,uBAAA7B,aAAA;QACA,KAAA+C,QAAA,CAAAiG,OAAA;QACA;MACA;;MAEA;MAAA,IAAAkE,UAAA,OAAA7G,2BAAA,CAAA1C,OAAA,EACA,KAAA3D,aAAA;QAAAmN,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAA5G,CAAA,MAAA6G,MAAA,GAAAD,UAAA,CAAAhJ,CAAA,IAAAqC,IAAA;UAAA,IAAAjF,IAAA,GAAA6L,MAAA,CAAA3G,KAAA;UACA,IAAAlF,IAAA,CAAAuD,iBAAA,KAAAvD,IAAA,CAAAwD,OAAA;YACA,KAAA/B,QAAA,CAAAiG,OAAA,kBAAAwC,MAAA,CAAAlK,IAAA,CAAAC,YAAA,mDAAAiK,MAAA,CAAAlK,IAAA,CAAAuD,iBAAA,sCAAA2G,MAAA,CAAAlK,IAAA,CAAAwD,OAAA;YACA;UACA;QACA;MAAA,SAAA3B,GAAA;QAAA+J,UAAA,CAAAxG,CAAA,CAAAvD,GAAA;MAAA;QAAA+J,UAAA,CAAAvG,CAAA;MAAA;MAEA,IAAAsC,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAlM,MAAA,QAAAA,MAAA;MACAkM,YAAA,CAAAjM,OAAA,QAAAA,OAAA;MACAiM,YAAA,CAAAE,IAAA,yBAAAnJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MAEA,IAAAmI,eAAA;MACAA,eAAA,CAAApK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACAyM,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAsJ,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAsJ,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACA4L,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA4L,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA3J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA4L,eAAA,CAAA5L,UAAA;QACA4L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAV,KAAA;QACAC,gBAAA,OAAAtJ,aAAA;QACAuJ,QAAA,EAAAN,YAAA;QACApB,SAAA,EAAA8B,eAAA;QACAtJ,WAAA,OAAAA;MACA;MAEAuB,OAAA,CAAAC,GAAA,iDAAAwH,KAAA,OAAAjJ,YAAA,CAAAlD,QAAA;MAGA,IAAAsM,kDAAA,EAAAH,KAAA,EAAA1G,IAAA,WAAAC,GAAA;QACAhB,OAAA,CAAAC,GAAA,iDAAAe,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAoK,OAAA,CAAAlK,QAAA,CAAA2F,OAAA;UACAuE,OAAA,CAAAzI,cAAA,CAAAyI,OAAA,CAAAlQ,MAAA;UACAkQ,OAAA,CAAA7I,WAAA;QACA;UACA;UACA6I,OAAA,CAAAlK,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAvB,OAAA,CAAAoB,KAAA,gCAAAG,GAAA;QACA8J,OAAA,CAAAlK,QAAA,CAAAC,KAAA;MACA;MAEA,KAAA5D,iBAAA;IACA;IAGAgO,cAAA,WAAAA,eAAA;MAEA,KAAA/N,iBAAA;IACA;IAEAyC,iBAAA,WAAAA,kBAAA;MACA,KAAA1B,YAAA;IACA;IAEAgE,WAAA,WAAAA,YAAA;MAAA,IAAAiJ,OAAA;MAAA,WAAA3J,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAyJ,SAAA;QAAA,IAAAtH,QAAA,EAAAuH,GAAA;QAAA,WAAA3J,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAwJ,SAAA;UAAA,kBAAAA,SAAA,CAAAtJ,CAAA;YAAA;cAAAsJ,SAAA,CAAArJ,CAAA;cAAAqJ,SAAA,CAAAtJ,CAAA;cAAA,OAEA,IAAAuJ,aAAA,EAAAJ,OAAA,CAAAlN,UAAA;YAAA;cAAA6F,QAAA,GAAAwH,SAAA,CAAA9I,CAAA;cACA2I,OAAA,CAAAjN,YAAA,GAAA4F,QAAA,CAAArJ,IAAA;cACAiF,OAAA,CAAAC,GAAA,sBAAAwL,OAAA,CAAAjN,YAAA;cACA,IAAAiN,OAAA,CAAAjN,YAAA,CAAAsN,iBAAA;gBACAL,OAAA,CAAAjN,YAAA,CAAAsN,iBAAA;cACA,WAAAL,OAAA,CAAAjN,YAAA,CAAAsN,iBAAA;gBACAL,OAAA,CAAAjN,YAAA,CAAAsN,iBAAA;cACA,WAAAL,OAAA,CAAAjN,YAAA,CAAAsN,iBAAA;gBACAL,OAAA,CAAAjN,YAAA,CAAAsN,iBAAA;cACA,WAAAL,OAAA,CAAAjN,YAAA,CAAAsN,iBAAA;gBACAL,OAAA,CAAAjN,YAAA,CAAAsN,iBAAA;cACA;cACA9L,OAAA,CAAAC,GAAA,sBAAAwL,OAAA,CAAAjN,YAAA;cACA;cACAiN,OAAA,CAAAM,SAAA;gBACAN,OAAA,CAAAlD,WAAA;cACA;cAAA,OAAAqD,SAAA,CAAA7I,CAAA,IACAqB,QAAA;YAAA;cAAAwH,SAAA,CAAArJ,CAAA;cAAAoJ,GAAA,GAAAC,SAAA,CAAA9I,CAAA;cAEA9C,OAAA,CAAAoB,KAAA,uBAAAuK,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA7I,CAAA;UAAA;QAAA,GAAA2I,QAAA;MAAA;IAGA;IAGAM,aAAA,WAAAA,cAAAC,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IAEA;IACAE,iBAAA,WAAAA,kBAAAF,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IACA;IACAG,wBAAA,WAAAA,yBAAAH,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IAEA;IACAI,2BAAA,WAAAA,4BAAAJ,QAAA;MACA,IAAAK,OAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAL,QAAA;IACA;IAEA;IACAM,qBAAA,WAAAA,sBAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA;IACAE,qBAAA,WAAAA,sBAAAF,MAAA;MACA,IAAAF,OAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAE,MAAA;IACA;IAEA;IACAG,WAAA,WAAAA,YAAA1M,GAAA;MACA,IAAA2M,eAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,eAAA,CAAA3M,GAAA,CAAA4M,IAAA;IACA;IAEA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAA1O,UAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACA;IACA2O,wBAAA,WAAAA,yBAAAC,GAAA;MACA,KAAAA,GAAA;QACA,KAAAlS,kBAAA,CAAAkC,WAAA;MACA;IACA;IACAiQ,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA,KAAAhQ,mBAAA;MACA,KAAAiQ,WAAA;MACA;MACA,KAAA3O,cAAA;MACA,KAAAoN,SAAA;QACA,IAAAsB,OAAA,CAAA5E,KAAA,CAAA8E,WAAA;UACAF,OAAA,CAAA5E,KAAA,CAAA8E,WAAA,CAAAC,cAAA;QACA;MACA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,SAAA;MACA;MACA,IAAAA,SAAA,CAAAnO,MAAA;QACA,IAAAoO,YAAA,GAAAD,SAAA,CAAAA,SAAA,CAAAnO,MAAA;QACA,KAAAkJ,KAAA,CAAA8E,WAAA,CAAAC,cAAA;QACA,KAAA/E,KAAA,CAAA8E,WAAA,CAAAK,kBAAA,CAAAD,YAAA;QACA,KAAAhP,cAAA,GAAAgP,YAAA;MACA;QACA,KAAAhP,cAAA,GAAA+O,SAAA;MACA;IACA;IACAG,sBAAA,WAAAA,uBAAA;MACA,UAAAlP,cAAA;QACA,KAAAwC,QAAA,CAAAiG,OAAA;QACA;MACA;MAEA,KAAAnM,kBAAA,CAAAkC,WAAA,QAAAwB,cAAA,CAAAvD,OAAA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,KAAAiC,mBAAA;MACA,KAAA8D,QAAA,CAAA2F,OAAA;IAIA;IACAwG,WAAA,WAAAA,YAAA;MAAA,IAAAQ,OAAA;MACA;MACA,KAAAvQ,UAAA,QAAAyB,oBAAA;MACA,KAAAzB,UAAA,CAAAqE,OAAA,WAAAlC,IAAA;QACAA,IAAA,CAAAqO,UAAA,GAAAD,OAAA,CAAA3B,iBAAA,CAAAzM,IAAA,CAAAqO,UAAA;MACA;MACA/N,OAAA,CAAAC,GAAA,oBAAA1C,UAAA;IACA;IACAyQ,uBAAA,WAAAA,wBAAAC,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IACAE,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAAC,YAAA,SAAA/Q,UAAA,CAAAjC,MAAA,QAAAiT,WAAA;MACA,IAAAC,aAAA,SAAAjR,UAAA,CAAAlC,OAAA,QAAAkT,WAAA;MACA,IAAAE,oBAAA,SAAAlR,UAAA,CAAAL,cAAA,QAAAqR,WAAA;;MAEA;MACA,KAAA/Q,UAAA,QAAAyB,oBAAA,CAAAiG,MAAA,WAAAvF,IAAA;QACA,IAAArE,MAAA,IAAAqE,IAAA,CAAArE,MAAA,QAAAoT,QAAA,GAAAH,WAAA;QACA,IAAAlT,OAAA,IAAAsE,IAAA,CAAAtE,OAAA,QAAAqT,QAAA,GAAAH,WAAA;QACA,IAAArR,cAAA,IAAAyC,IAAA,CAAAzC,cAAA,QAAAwR,QAAA,GAAAH,WAAA;;QAEA;QACA,IAAAI,WAAA,IAAAL,YAAA,IAAAhT,MAAA,CAAA6J,QAAA,CAAAmJ,YAAA;QACA,IAAAM,YAAA,IAAAJ,aAAA,IAAAnT,OAAA,CAAA8J,QAAA,CAAAqJ,aAAA;QACA,IAAAK,mBAAA,IAAAJ,oBAAA,IAAAvR,cAAA,CAAAiI,QAAA,CAAAsJ,oBAAA;QAEA,OAAAE,WAAA,IAAAC,YAAA,IAAAC,mBAAA;MACA;;MAEA;MACA,KAAArR,UAAA,CAAAqE,OAAA,WAAAlC,IAAA;QACAA,IAAA,CAAAqO,UAAA,GAAAK,OAAA,CAAAjC,iBAAA,CAAAzM,IAAA,CAAAqO,UAAA;MACA;IACA;IACAc,WAAA,WAAAA,YAAA;MACA,KAAAvR,UAAA;QACAjC,MAAA;QACAD,OAAA;QACA6B,cAAA;MACA;MACA,KAAAqQ,WAAA;IACA;IACAwB,eAAA,WAAAA,gBAAAjC,IAAA;MACA,IAAAP,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAO,IAAA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACAkC,qBAAA,WAAAA,sBAAArB,SAAA;MACA,KAAAxO,YAAA,GAAAwO,SAAA;IACA;IAEA;IACAsB,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,OAAA;MACA,IAAAtN,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAuD,QAAA;QACA,KAAA/D,QAAA,CAAAC,KAAA;QACA;MACA;MACA,IAAA8N,SAAA;MACA,KAAAhQ,YAAA,CAAA0C,OAAA,WAAAlC,IAAA;QACA,IAAAA,IAAA,CAAAuD,iBAAA,KAAAvD,IAAA,CAAAwD,OAAA;UACA+L,OAAA,CAAA9N,QAAA,CAAAiG,OAAA;UACA8H,SAAA;QACA;MACA;MAEA,IAAAA,SAAA;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA,IAAA7H,YAAA;QACAC,OAAA;QACAnM,MAAA,OAAAA,MAAA;QACAC,OAAA,OAAAA,OAAA;QACAmM,IAAA,wBAAAnJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,YAAA;QAAA,GAAAC,IAAA;MACA;;MAEA;MACA,IAAA4H,eAAA;QACA7J,EAAA,OAAAa,YAAA,CAAAb,EAAA;QACApC,eAAA;QAAA;QACAC,aAAA,MAAAgI,IAAA;QACArH,UAAA;MACA;MAEA,KAAA+C,YAAA,CAAA0C,OAAA,WAAAlC,IAAA;QACA;QACAA,IAAA,CAAAyP,iBAAA,GAAAzP,IAAA,CAAAuD,iBAAA;MACA;;MAEA;MACA,IAAAwE,KAAA;QACAC,gBAAA,OAAAxI,YAAA;QAAA;QACAyI,QAAA,EAAAN,YAAA;QACApB,SAAA,EAAAuB,eAAA;QACA/I,WAAA,OAAAA;MACA;;MAEA;MACA,IAAAmJ,kDAAA,EAAAH,KAAA,EAAA1G,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAgO,OAAA,CAAA9N,QAAA,CAAA2F,OAAA;UACAmI,OAAA,CAAArM,cAAA,CAAAqM,OAAA,CAAA9T,MAAA;UACA8T,OAAA,CAAAzM,WAAA;UACA;UACAyM,OAAA,CAAA/P,YAAA;QACA;UACA+P,OAAA,CAAA9N,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAvB,OAAA,CAAAoB,KAAA,0CAAAG,GAAA;QACA0N,OAAA,CAAA9N,QAAA,CAAAC,KAAA;MACA;IACA;IACAgO,iBAAA,WAAAA,kBAAA;MACApP,OAAA,CAAAC,GAAA,2BAAAd,kBAAA;MACA,IAAAZ,UAAA,QAAAY,kBAAA,CAAAZ,UAAA;MACA,IAAAnD,OAAA,GAAAiU,MAAA,MAAAlQ,kBAAA,CAAA/D,OAAA;MACA,IAAAqD,WAAA,QAAAU,kBAAA,CAAAV,WAAA;MACA,IAAA6B,QAAA,QAAAnB,kBAAA,CAAAmB,QAAA;MACA,IAAAnF,MAAA,GAAAkU,MAAA,MAAAlQ,kBAAA,CAAAhE,MAAA;MACA,IAAAmU,GAAA,kDAAA1F,MAAA,CAAArL,UAAA,eAAAqL,MAAA,CAAAxO,OAAA,mBAAAwO,MAAA,CAAAnL,WAAA,gBAAAmL,MAAA,CAAAtJ,QAAA,cAAAsJ,MAAA,CAAAzO,MAAA;MACAwI,MAAA,CAAAC,IAAA,CAAA0L,GAAA;IACA;EACA;AACA", "ignoreList": []}]}